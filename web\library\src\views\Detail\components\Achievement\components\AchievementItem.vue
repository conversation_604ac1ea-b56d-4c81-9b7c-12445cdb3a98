<template>
  <div
    class="cpt-achievement-item"
    :class="{ grey: !item.achieved }"
  >
    <div class="img-cover"></div>
    <img
      :class="item.finishLoad ? 'fade' : 'none'"
      :src="item.icon"
    />
    <div
      class="gold-ring"
      v-if="item.achieved"
    ></div>
  </div>
</template>

<script setup name="AchievementItem">
import { defineProps } from 'vue';

const props = defineProps({
  item: {
    type: Object,
  },
});
</script>

<style lang="scss">
.cpt-achievement-item {
  position: relative;
  width: 34px;
  height: 34px;
  padding: 2px;
  border-radius: 2px;
  &.achieved {
    .img-cover {
      background: var(---general-color-primary-0, #fff);
    }
  }
  img {
    position: relative;
    z-index: 4;
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 2px;
  }
  .img-cover {
    position: absolute;
    z-index: 3;
    top: 2px;
    left: 2px;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border-radius: 3px;
    background: gray;
  }
  &:last-child {
    margin-right: 0;
  }
}
</style>
