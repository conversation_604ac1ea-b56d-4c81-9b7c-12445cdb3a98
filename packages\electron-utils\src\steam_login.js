const fs = require("node:fs/promises");
const path = require("path");
const http = require('http');
const { readVdfFile, writeVdfFile, extractZip } = require('./fileHandler')
const requireAddon = require('./require_addon')
const toolHelper = requireAddon('tool_helper')
const os = require('os');
const { app } = require('electron');
const SteamUser = require('@heybox/steam-user');
const log = require('./log')

/**
 * 不区分大小写的路径访问函数
 * @param {Object} obj - 要访问的对象
 * @param {string} path - 点分隔的路径字符串，如 "MachineUserConfigStore.Software.Valve.Steam.ConnectCache"
 * @returns {*} 返回路径对应的值，如果路径不存在则返回 undefined
 */
function getCaseInsensitivePath(obj, pathStr) {
  if (!obj || !pathStr) return undefined
  
  const pathParts = pathStr.split('.')
  let current = obj
  
  for (const part of pathParts) {
    if (current === null || current === undefined) return undefined
    
    // 如果当前层级是数组，直接返回 undefined
    if (Array.isArray(current)) return undefined
    
    // 尝试直接访问（保持原有大小写）
    if (current.hasOwnProperty(part)) {
      current = current[part]
      continue
    }
    
    // 尝试不区分大小写访问
    const keys = Object.keys(current)
    const foundKey = keys.find(key => key.toLowerCase() === part.toLowerCase())
    
    if (foundKey) {
      current = current[foundKey]
    } else {
      return undefined
    }
  }
  
  return current
}

let isLoginSuccess = false

function setDeep(obj, pathArr, value) {
  let current = obj;
  for (let i = 0; i < pathArr.length - 1; i++) {
    if (!(pathArr[i] in current)) {
      current[pathArr[i]] = {};
    }
    current = current[pathArr[i]];
  }
  current[pathArr[pathArr.length - 1]] = value;
}

function getDeep(obj, pathArr) {
  let current = obj;
  for (let i = 0; i < pathArr.length; i++) {
    if (!(pathArr[i] in current)) {
      return undefined;
    }
    current = current[pathArr[i]];
  }
  return current;
}

function crc32(str) {
  var b_table =
    globalThis._crc32_table_a ||
    (globalThis._crc32_table_a =
      "00000000 77073096 EE0E612C 990951BA 076DC419 706AF48F E963A535 9E6495A3 0EDB8832 79DCB8A4 E0D5E91E 97D2D988 09B64C2B 7EB17CBD E7B82D07 90BF1D91 1DB71064 6AB020F2 F3B97148 84BE41DE 1ADAD47D 6DDDE4EB F4D4B551 83D385C7 136C9856 646BA8C0 FD62F97A 8A65C9EC 14015C4F 63066CD9 FA0F3D63 8D080DF5 3B6E20C8 4C69105E D56041E4 A2677172 3C03E4D1 4B04D447 D20D85FD A50AB56B 35B5A8FA 42B2986C DBBBC9D6 ACBCF940 32D86CE3 45DF5C75 DCD60DCF ABD13D59 26D930AC 51DE003A C8D75180 BFD06116 21B4F4B5 56B3C423 CFBA9599 B8BDA50F 2802B89E 5F058808 C60CD9B2 B10BE924 2F6F7C87 58684C11 C1611DAB B6662D3D 76DC4190 01DB7106 98D220BC EFD5102A 71B18589 06B6B51F 9FBFE4A5 E8B8D433 7807C9A2 0F00F934 9609A88E E10E9818 7F6A0DBB 086D3D2D 91646C97 E6635C01 6B6B51F4 1C6C6162 856530D8 F262004E 6C0695ED 1B01A57B 8208F4C1 F50FC457 65B0D9C6 12B7E950 8BBEB8EA FCB9887C 62DD1DDF 15DA2D49 8CD37CF3 FBD44C65 4DB26158 3AB551CE A3BC0074 D4BB30E2 4ADFA541 3DD895D7 A4D1C46D D3D6F4FB 4369E96A 346ED9FC AD678846 DA60B8D0 44042D73 33031DE5 AA0A4C5F DD0D7CC9 5005713C 270241AA BE0B1010 C90C2086 5768B525 206F85B3 B966D409 CE61E49F 5EDEF90E 29D9C998 B0D09822 C7D7A8B4 59B33D17 2EB40D81 B7BD5C3B C0BA6CAD EDB88320 9ABFB3B6 03B6E20C 74B1D29A EAD54739 9DD277AF 04DB2615 73DC1683 E3630B12 94643B84 0D6D6A3E 7A6A5AA8 E40ECF0B 9309FF9D 0A00AE27 7D079EB1 F00F9344 8708A3D2 1E01F268 6906C2FE F762575D 806567CB 196C3671 6E6B06E7 FED41B76 89D32BE0 10DA7A5A 67DD4ACC F9B9DF6F 8EBEEFF9 17B7BE43 60B08ED5 D6D6A3E8 A1D1937E 38D8C2C4 4FDFF252 D1BB67F1 A6BC5767 3FB506DD 48B2364B D80D2BDA AF0A1B4C 36034AF6 41047A60 DF60EFC3 A867DF55 316E8EEF 4669BE79 CB61B38C BC66831A 256FD2A0 5268E236 CC0C7795 BB0B4703 220216B9 5505262F C5BA3BBE B2BD0B28 2BB45A92 5CB36A04 C2D7FFA7 B5D0CF31 2CD99E8B 5BDEAE1D 9B64C2B0 EC63F226 756AA39C 026D930A 9C0906A9 EB0E363F 72076785 05005713 95BF4A82 E2B87A14 7BB12BAE 0CB61B38 92D28E9B E5D5BE0D 7CDCEFB7 0BDBDF21 86D3D2D4 F1D4E242 68DDB3F8 1FDA836E 81BE16CD F6B9265B 6FB077E1 18B74777 88085AE6 FF0F6A70 66063BCA 11010B5C 8F659EFF F862AE69 616BFFD3 166CCF45 A00AE278 D70DD2EE 4E048354 3903B3C2 A7672661 D06016F7 4969474D 3E6E77DB AED16A4A D9D65ADC 40DF0B66 37D83BF0 A9BCAE53 DEBB9EC5 47B2CF7F 30B5FFE9 BDBDF21C CABAC28A 53B39330 24B4A3A6 BAD03605 CDD70693 54DE5729 23D967BF B3667A2E C4614AB8 5D681B02 2A6F2B94 B40BBE37 C30C8EA1 5A05DF1B 2D02EF8D"
        .split(" ")
        .map(function (s) {
          return parseInt(s, 16);
        }));
  var crc = -1;
  for (var i = 0, iTop = str.length; i < iTop; i++) {
    crc = (crc >>> 8) ^ b_table[(crc ^ str.charCodeAt(i)) & 0xff];
  }
  return (crc ^ -1) >>> 0;
}

async function fileExists(file) {
  return await fs
    .access(file, fs.constants.F_OK)
    .then(() => true)
    .catch(() => false);
}

async function ensureFileExists(filePath, content = "") {
  const dir = path.dirname(filePath);
  await fs.mkdir(dir, { recursive: true });
  try {
    await fs.access(filePath, fs.constants.F_OK);
  } catch {
    await fs.writeFile(filePath, content, "utf8");
  }
}

// TODO: 获取 Steam 安装目录
const steamLocalAppData = process.env.LOCALAPPDATA + "\\Steam";

function getRegeditKey(path, key) {
  const regedit = require('./regedit')
  return new Promise((resolve, reject) => {
    try {
      resolve(regedit.getKey(path, key))
    } catch (error) {
      reject(error.message)
    }
  })
}

async function getSteamInstallDir() {
  return await getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
}

async function getSteamId(accountName) {
  const steamInstallDir = await getSteamInstallDir()
  const configPath = steamInstallDir + "\\config\\config.vdf";
  const config = readVdfFile(configPath);
  const targetKeyPrefix = crc32(accountName).toString(16);
  const connectCache = config.InstallConfigStore.Software.Valve.Steam.ConnectCache;
  for (const key in connectCache) {
    if (key.startsWith(targetKeyPrefix)) {
      isLoginSuccess = true;
    }
  }
  return config?.InstallConfigStore?.Software?.Valve?.Steam?.Accounts[
    accountName
  ]?.SteamID;
}

async function AddUserIntoLoginusersVdf(accountName, steamid) {
  const steamInstallDir = await getSteamInstallDir()
  const loginusersPath = steamInstallDir + "\\config\\loginusers.vdf";
  await ensureFileExists(loginusersPath);
  const loginusers = readVdfFile(loginusersPath);
  for (const id in loginusers.users) {
    loginusers.users[id].MostRecent = "0"; // 将其他账号的 MostRecent 设置为 0
  }
  setDeep(loginusers, ["users", steamid], {
    AccountName: accountName,
    PersonaName: getDeep(loginusers, ["users", steamid, "PersonaName"]) ?? "", // Steam 客户端登录后会自动更新
    RememberPassword: "1",
    WantsOfflineMode: "0",
    SkipOfflineModeWarning: "1",
    AllowAutoLogin: "1",
    MostRecent: "1",
    Timestamp: Math.trunc(Date.now() / 1000).toString(),
  });
  writeVdfFile(loginusersPath, loginusers);
}

async function clearUserToken(accountName) {
  const localVdfPath = steamLocalAppData + "\\local.vdf";
  const targetKeyPrefix = crc32(accountName).toString(16);

  // 90 秒超时
  let count = 0;
  while (++count <= 90) {
    do {
      if (!fileExists(localVdfPath)) {
        break;
      }

      const local = readVdfFile(localVdfPath);
      const connectCache =
        getCaseInsensitivePath(local, 'MachineUserConfigStore.Software.Valve.Steam.ConnectCache');
      if (!connectCache) {
        break;
      }

      let targetKey;
      for (const key in connectCache) {
        if (key.startsWith(targetKeyPrefix)) {
          targetKey = key;
          break;
        }
      }
      if (!targetKey) {
        break;
      }

      delete connectCache[targetKey];
      writeVdfFile(localVdfPath, local);
      return;
    } while (false);

    await sleep(1000);
  }

  // 如果 steam 本地 token 处理超时（不太可能发生），则清理 steamcmd 生成的原始 token，避免用户敏感数据泄露
  const steamInstallDir = await getSteamInstallDir()
  const configVdfPath = steamInstallDir + "\\config.vdf";

  const config = readVdfFile(configVdfPath);
  const connectCache =
    getCaseInsensitivePath(config, 'InstallConfigStore.Software.Valve.Steam.ConnectCache');

  for (const key in connectCache) {
    if (key.startsWith(targetKeyPrefix)) {
      delete connectCache[targetKey];
      writeVdfFile(configVdfPath, config);
    }
  }
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function loginSteamAccount(options, callbacks = {}) {
  const {
    onGuardCode,
    onAppConfirm,
    onProgress,
    onSuccess,
    onError
  } = callbacks;

  isLoginSuccess = false
  let isWaitingCode = false
  // 首先确保SteamCMD已初始化
  try {
    console.log('检查SteamCMD状态...')
    const initResult = await initSteamCmd(onProgress)
    if (!initResult.success) {
      const error = new Error(`SteamCMD初始化失败: ${initResult.error}`)
      if (onError) onError(error)
      return
    }
    console.log('SteamCMD初始化成功:', initResult.message)
  } catch (error) {
    console.error('SteamCMD初始化异常:', error)
    if (onError) onError(error)
    return
  }

  return new Promise((resolve, reject) => {
    toolHelper.loginSteamCmd(async (event, param2) => {
      console.log('event', event)
      if (event === "guardCode") {
        // 需要输入邮箱验证码，若输入错误则会重复触发 guarCode 事件
        var count = count + 1 || 0;
        
        if (onGuardCode) {
          try {
            const code = await onGuardCode(count);
            param2(code);
          } catch (error) {
            isWaitingCode = false
            console.log(error)
          }
        }
      } else if (event === "appConfirm") {
        // 需要用户手机 app 确认登录
        if (onAppConfirm) {
          try {
            const result = await onAppConfirm();
            if(isLoginSuccess) {
              reject()
            }
            if (result && result.totpCode) {
              options.totpCode = result.totpCode
              param2();
            }
          } catch (error) {
            console.log(error)
          }
        }
      } else if (event === "killed") {
        // 重新启动登录流程
        if (options.totpCode) {
          return await loginSteamAccount({ ...options, totpCode: options.totpCode }, callbacks);
        }
      } else if (event === "done" || event === "error") {
        // 即使 steamcmd 返回 error，但可能 token 已经被获取
        console.log('event', event, param2)
        try {
          const steamid = await getSteamId(options.accountName);
          if (!steamid || !isLoginSuccess) {
            const error = param2;
            if (onError) onError(error);
            reject(error);
            return;
          }
          
          // 修改 loginusers.vdf 添加账号
          await AddUserIntoLoginusersVdf(options.accountName, steamid);
          isLoginSuccess = await checkIsLoginSuccess(options.accountName)
          if(!isLoginSuccess) {
            const error = new Error('登录失败')
            if (onError) onError(error)
            reject(error)
            return
          } else {
            // 启动 Steam 客户端 登录该账号
            clearLocalConnectCache(options.accountName)
            toolHelper.switchSteamAccount(
              async (err) => {
                if (err) {
                  if (onError) onError(err);
                  reject(err);
                } else {
                  if (onSuccess) onSuccess({ accountName: options.accountName, steamid });
                  resolve({ accountName: options.accountName, steamid });
                }
              }, {
                accountName: options.accountName,
              }
            );
          }
        } catch (error) {
          if (onError) onError(error);
          reject(error);
        }
      }
    }, options);
  });
}

function clearLocalConnectCache(accountName) {
  // 清理旧的登录token缓存（防止因为token没更新导致登录失败的问题）
  const localVdfPath = steamLocalAppData + "\\local.vdf";
  const localVdf = readVdfFile(localVdfPath)
  const connectCache = getCaseInsensitivePath(localVdf, 'MachineUserConfigStore.Software.Valve.Steam.ConnectCache');
  const key = crc32(accountName).toString(16) + "1";
  delete connectCache[key]
  writeVdfFile(localVdfPath, localVdf)
}

async function checkIsLoginSuccess(accountName) {
  const steamInstallDir = await getSteamInstallDir()
  const configPath = steamInstallDir + "\\config\\config.vdf";
  const config = readVdfFile(configPath);
  const targetKeyPrefix = crc32(accountName).toString(16);
  
  const connectCache = getCaseInsensitivePath(config, 'InstallConfigStore.Software.Valve.Steam.ConnectCache');
  let loginSuccessed = false;
  for (const key in connectCache) {
    if (key.startsWith(targetKeyPrefix)) {
      loginSuccessed = true;
    }
  }
  return loginSuccessed
}

// SteamCMD初始化相关函数

/**
 * 从Steam CDN获取steamcmd版本信息
 */
async function getSteamCmdVersionInfo() {
  try {
    console.log('获取SteamCMD版本信息...')
    
    const steamCmdVdfUrl = 'http://cdn.steamstatic.com/client/steam_cmd_win32'
    
    return new Promise((resolve, reject) => {
      const client = http
      
      const request = client.get(steamCmdVdfUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`))
          return
        }

        let data = ''
        response.on('data', (chunk) => {
          data += chunk
        })

        response.on('end', () => {
          try {
            const steamCmdInfo = parseSteamCmdVdf(data)
            if (!steamCmdInfo || !steamCmdInfo.file) {
              reject(new Error('无法解析SteamCMD版本信息'))
              return
            }
            
            console.log('SteamCMD版本信息:', steamCmdInfo)
            resolve(steamCmdInfo)
          } catch (error) {
            reject(new Error(`解析VDF失败: ${error.message}`))
          }
        })
      })

      request.on('error', (error) => {
        reject(error)
      })

      request.setTimeout(10000, () => {
        request.destroy()
        reject(new Error('获取版本信息超时'))
      })
    })

  } catch (error) {
    console.error('获取SteamCMD版本信息失败:', error)
    throw error
  }
}

/**
 * 解析SteamCMD VDF内容，提取steamcmd_win32信息
 */
function parseSteamCmdVdf(content) {
  try {
    const lines = content.split('\n')
    let inWin32Block = false
    let steamCmdInfo = {}

    for (const line of lines) {
      const trimmedLine = line.trim()
      
      if (trimmedLine === '"win32"') {
        inWin32Block = true
        continue
      }

      if (inWin32Block && trimmedLine.startsWith('"steamcmd_win32"')) {
        // 找到steamcmd_win32块
        steamCmdInfo = extractSteamCmdInfo(lines, lines.indexOf(line))
        break
      }
    }

    return steamCmdInfo

  } catch (error) {
    console.error('解析VDF内容失败:', error)
    return null
  }
}

/**
 * 从VDF内容中提取steamcmd_win32的详细信息
 */
function extractSteamCmdInfo(lines, startIndex) {
  try {
    const info = {}
    let braceCount = 0
    let inSteamCmdBlock = false

    for (let i = startIndex; i < lines.length; i++) {
      const line = lines[i].trim()
      
      if (line === '"steamcmd_win32"') {
        inSteamCmdBlock = true
        continue
      }

      if (inSteamCmdBlock) {
        if (line === '{') {
          braceCount++
          continue
        }

        if (line === '}') {
          braceCount--
          if (braceCount === 0) {
            break
          }
          continue
        }

        // 解析键值对
        const match = line.match(/"([^"]+)"\s+"([^"]+)"/)
        if (match) {
          const [, key, value] = match
          info[key] = value
        }
      }
    }

    return info

  } catch (error) {
    console.error('提取SteamCMD信息失败:', error)
    return {}
  }
}

/**
 * 下载SteamCMD文件
 */
async function downloadSteamCmd(versionInfo, onProgress) {
  try {
    console.log('开始下载SteamCMD...')
    
    if (!versionInfo.file) {
      throw new Error('版本信息中缺少文件信息')
    }

    const steamCdnBase = 'http://cdn.steamstatic.com/client/'
    const downloadUrl = steamCdnBase + versionInfo.file
    const fileName = versionInfo.file.split('/').pop() || 'steamcmd.zip'
    const tempDir = process.env.TEMP || 'C:\\Windows\\Temp'
    const filePath = path.join(tempDir, fileName)

    console.log('下载URL:', downloadUrl)
    console.log('保存路径:', filePath)

    return new Promise((resolve, reject) => {
      const client = http

      const request = client.get(downloadUrl, (response) => {
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`))
          return
        }

        const fileStream = require('fs').createWriteStream(filePath)
        
        // 获取文件大小用于进度显示
        const totalSize = parseInt(response.headers['content-length'], 10)
        let downloadedSize = 0

        response.on('data', (chunk) => {
          downloadedSize += chunk.length
          const progress = totalSize ? Math.floor(downloadedSize / totalSize * 100) : 0

          if (onProgress && typeof onProgress === 'function') {
            onProgress(progress)
          }
        })

        fileStream.on('finish', () => {
          fileStream.close()
          console.log('SteamCMD下载完成')

          if (onProgress && typeof onProgress === 'function') {
            onProgress(100)
          }

          resolve({
            success: true,
            filePath: filePath,
            fileName: fileName,
            size: downloadedSize
          })
        })

        fileStream.on('error', (error) => {
          require('fs').unlink(filePath, () => {}) // 删除不完整的文件
          reject(error)
        })

        response.pipe(fileStream)
      })

      request.on('error', (error) => {
        reject(error)
      })

      request.setTimeout(30000, () => {
        request.destroy()
        reject(new Error('下载超时'))
      })
    })

  } catch (error) {
    console.error('下载SteamCMD失败:', error)
    throw error
  }
}

/**
 * 解压SteamCMD到Steam安装目录
 */
async function extractSteamCmd(zipFilePath) {
  try {
    console.log('开始解压SteamCMD...')
    
    const steamInstallDir = await getSteamInstallDir()
    const result = await extractZip(zipFilePath, steamInstallDir)
    
    console.log('SteamCMD解压完成')
    return { success: true, data: result }

  } catch (error) {
    console.error('解压SteamCMD失败:', error)
    throw error
  }
}

/**
 * 检查steamcmd是否存在
 */
async function checkSteamCmdExists() {
  try {
    const steamInstallDir = await getSteamInstallDir()
    const steamCmdPath = path.join(steamInstallDir, 'steamcmd.exe')
    return await fs.access(steamCmdPath).then(() => true).catch(() => false)
  } catch (error) {
    console.error('检查SteamCMD存在失败:', error)
    return false
  }
}

/**
 * 初始化SteamCMD
 * 检查steamcmd是否存在，如果不存在则下载安装
 */
async function initSteamCmd(onProgress) {
  try {
    log.info('开始初始化SteamCMD...')
    
    // 检查steamcmd是否已存在
    const steamCmdExists = await checkSteamCmdExists()
    if (steamCmdExists) {
      log.info('SteamCMD已存在，跳过下载')
      return { success: true, message: 'SteamCMD已存在' }
    }

    // 获取最新版本信息
    const versionInfo = await getSteamCmdVersionInfo()
    if (!versionInfo) {
      throw new Error('无法获取SteamCMD版本信息')
    }

    // 下载steamcmd
    const downloadResult = await downloadSteamCmd(versionInfo, onProgress)
    if (!downloadResult.success) {
      throw new Error(downloadResult.error)
    }

    // 解压到Steam安装目录
    const extractResult = await extractSteamCmd(downloadResult.filePath)
    if (!extractResult.success) {
      throw new Error(extractResult.error)
    }

    // 清理临时文件
    try {
      await fs.unlink(downloadResult.filePath)
    } catch (error) {
      console.warn('清理临时文件失败:', error)
    }

    log.info('SteamCMD初始化完成')
    return { success: true, message: 'SteamCMD初始化完成' }

  } catch (error) {
    console.error('SteamCMD初始化失败:', error)
    return { success: false, error: error.message }
  }
}

let retryCount = 0
function getSteamUserData(accountName) {
  return new Promise((resolve, reject) => {
    log.info('[steam_get_user_data] getSteamUserData', accountName)
    const localVdfPath = steamLocalAppData + "\\local.vdf";
    const localVdf = readVdfFile(localVdfPath)
    const connectCache = getCaseInsensitivePath(localVdf, 'MachineUserConfigStore.Software.Valve.Steam.ConnectCache');
    const key = crc32(accountName).toString(16) + "1";
    const token = connectCache[key];
    if(!token) {
      // 等待steam登录后token写入local.vdf
      retryCount++
      log.info('[steam_get_user_data] retry', retryCount)
      if(retryCount < 5) {
        setTimeout(() => {
          getSteamUserData(accountName)
        }, 3000);
      } else {
        reject(new Error('connectCache token not found'))
      }
      return
    }
    retryCount = 0
    const refreshToken = toolHelper.decryptSteamToken({accountName, token})
    const steamClient = new SteamUser({
      dataDirectory: path.join(app.getPath('userData'), 'node-steamuser'),
      enablePicsCache: true,
      // protocol: SteamUser.EConnectionProtocol.TCP,
    });
    
    steamClient.on('error', (err) => {
      console.error(err);
      if(err.message === 'Request timed out') {
        // 超时重试
        setTimeout(() => {
          console.log('retry login')
          getSteamUserData(accountName)
        }, 1000);
      } else {
        reject(err)
      }
    });
    
    steamClient.logOn({
      refreshToken: refreshToken,
      logonID: Date.now(),
      machineName: os.hostname(),
    });
    steamClient.on('licenses', async (licenses) => {
      const pkgids = licenses.map(license => license.package_id);
      steamClient._sendUnified('Player.ClientGetLastPlayedTimes#1', {}, (body, hdr) => {
        let appinfos = body.games.map(game => {
          return {
            appid: game.appid,
            first_playtime: game.first_playtime,
            last_playtime: game.last_playtime,
            playtime_2weeks: game.playtime_2weeks,
            playtime_forever: game.playtime_forever,
          }
        })
        steamClient.logOff();
        resolve({
          appinfos,
          pkgids
        })
      });
    });
  })
}

// 导出其他可能需要的函数
module.exports = {
  getSteamId,
  AddUserIntoLoginusersVdf,
  loginSteamAccount,
  clearUserToken,
  initSteamCmd,
  getSteamCmdVersionInfo,
  downloadSteamCmd,
  extractSteamCmd,
  checkSteamCmdExists,
  getSteamUserData,
};
