<template>
  <div class="skeleton-sidebar">
    <div class="header-row">
      <div class="header-name">库</div>
      <div class="header-add-btn">
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_492_142)">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M9.9001 1.87461C9.9001 1.37755 9.49715 0.974609 9.0001 0.974609C8.50304 0.974609 8.1001 1.37755 8.1001 1.87461V8.09961H1.8751C1.37804 8.09961 0.975098 8.50255 0.975098 8.99961C0.975098 9.49667 1.37804 9.89961 1.8751 9.89961H8.1001V16.1246C8.1001 16.6217 8.50304 17.0246 9.0001 17.0246C9.49715 17.0246 9.9001 16.6217 9.9001 16.1246V9.89961H16.1251C16.6222 9.89961 17.0251 9.49667 17.0251 8.99961C17.0251 8.50255 16.6222 8.09961 16.1251 8.09961H9.9001V1.87461Z"
              fill="#8C9196"
            />
          </g>
          <defs>
            <clipPath id="clip0_492_142">
              <rect
                width="18"
                height="18"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-wrapper">
      <div class="search-input-wrapper">
        <div class="search-input" @click="handleSearchClick">
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M2.5 6C2.5 4.067 4.067 2.5 6 2.5C7.933 2.5 9.5 4.067 9.5 6C9.5 7.933 7.933 9.5 6 9.5C4.067 9.5 2.5 7.933 2.5 6ZM6 1C3.23858 1 1 3.23858 1 6C1 8.76142 3.23858 11 6 11C7.11016 11 8.13582 10.6382 8.96549 10.0261L10.2203 11.2808C10.5132 11.5737 10.9878 11.5735 11.2804 11.2803C11.573 10.9872 11.5728 10.5121 11.2799 10.2192L10.0261 8.96538C10.6382 8.13573 11 7.11011 11 6C11 3.23858 8.76142 1 6 1Z"
              fill="#8C9196"
            />
          </svg>
          <input
            type="text"
            placeholder="按名称搜索"
            disabled
          />
        </div>
      </div>
      <div class="filter-container">
        <div class="filter-option">
          <div class="filter-option-label">
            <i class="iconfont icon-common-filter2-filled"></i>
            <span>近两周时长</span>
          </div>
        </div>
        <div class="vertical-split-line"></div>
        <div class="filter-list-btn">
          <i class="iconfont icon-common-filter-filled"></i>
          <span>筛选</span>
        </div>
      </div>
    </div>

    <div class="divider"></div>

    <SkeletonGameList v-if="isLoggedIn" />
    <div v-else class="no-games-placeholder">
      <div class="no-games-placeholder">
        <div class="no-games-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.00004 4C4.52728 4 3.33337 5.19391 3.33337 6.66667V10.6667C3.33337 12.1394 4.52728 13.3333 6.00004 13.3333H11.3334C12.8061 13.3333 14 12.1394 14 10.6667V6.66667C14 5.19391 12.8061 4 11.3334 4H6.00004ZM6.00004 18.6667C4.52728 18.6667 3.33337 19.8606 3.33337 21.3333V25.3333C3.33337 26.8061 4.52728 28 6.00004 28H11.3334C12.8061 28 14 26.8061 14 25.3333V21.3333C14 19.8606 12.8061 18.6667 11.3334 18.6667H6.00004ZM17 6C17 5.26362 17.597 4.66667 18.3334 4.66667H29C29.7364 4.66667 30.3334 5.26362 30.3334 6C30.3334 6.73638 29.7364 7.33333 29 7.33333H18.3334C17.597 7.33333 17 6.73638 17 6ZM18.3334 19.3333C17.597 19.3333 17 19.9303 17 20.6667C17 21.403 17.597 22 18.3334 22H29C29.7364 22 30.3334 21.403 30.3334 20.6667C30.3334 19.9303 29.7364 19.3333 29 19.3333H18.3334ZM17 11.3333C17 10.597 17.597 10 18.3334 10H29C29.7364 10 30.3334 10.597 30.3334 11.3333C30.3334 12.0697 29.7364 12.6667 29 12.6667H18.3334C17.597 12.6667 17 12.0697 17 11.3333ZM18.3334 24.6667C17.597 24.6667 17 25.2636 17 26C17 26.7364 17.597 27.3333 18.3334 27.3333H29C29.7364 27.3333 30.3334 26.7364 30.3334 26C30.3334 25.2636 29.7364 24.6667 29 24.6667H18.3334Z" fill="#C8CDD2"/>
          </svg>
        </div>
        <div class="no-games-text">暂无游戏</div>
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonSidebar">
import SkeletonGameList from './SkeletonGameList.vue';

const props = defineProps({
  isLoggedIn: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['searchClick']);

const handleSearchClick = () => {
  emit('searchClick');
};
</script>

<style lang="scss" scoped>
.skeleton-sidebar {
  width: 100%;
  height: 100%;

  //  GameSideBar 的头部样式
  .header-row {
    width: 100%;
    height: 52px;
    padding: 0px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(---general-color-stroke-1, #f1f2f3);
    .header-name {
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
    }
    .header-add-btn {
      display: flex;
      padding: 7px;
      align-items: center;
      gap: 10px;
    }
  }

  // 复制 GameSideBar 的搜索区域样式
  .search-wrapper {
    width: 100%;
    display: flex;
    padding: 8px 8px 0px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;
    .search-input-wrapper {
      padding: 0 8px;
      width: 100%;
    }
    .search-input {
      width: 100%;
      display: flex;
      height: 32px;
      padding: 7px 12px;
      align-items: center;
      gap: 3px;
      flex: 1 0 0;
      border-radius: var(---32, 5px);
      border: 1px solid var(---general-color-stroke-2, #F3F4F5);
      background: var(---general-color-bg-3, #FAFBFC);
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
      input {
        display: flex;
        flex: 1 0 0;
        outline: none;
        border: none;
        background: transparent;
        font-size: 14px;
        line-height: 20px;
        color: var(---general-color-text-1, #111111);
        &::placeholder {
          color: var(---general-color-text-3, #8C9196);
          font-family: "PingFang SC";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
        }
      }
    }
    .filter-container {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 8px;
      & > div {
        cursor: pointer;
      }
      .filter-option {
        flex: 1;
        height: 24px;
        .filter-option-label {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 3px;
          .iconfont {
            font-size: 12px;
            color: #8c9196;
          }
        }
        span {
          color: #8c9196;
          text-align: center;
          font-size: 12px;
          line-height: 16px;
        }
      }
      .vertical-split-line {
        width: 1px;
        height: 16px;
        background-color: #f1f2f3;
      }
      .filter-list-btn {
        flex: 1;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 3px;
        span {
          color: #8c9196;
          text-align: center;
          font-size: 12px;
          line-height: 16px;
        }
        .iconfont {
          color: #8c9196;
          font-size: 12px;
        }
      }
    }
  }

  // 复制 GameSideBar 的分隔线样式
  .divider {
    margin: 6px 16px;
    height: 1px;
    align-self: stretch;
    background-color: #e8e9ea;
  }

.no-games-placeholder {
  display: flex;
  padding: 50px 36px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  flex: 1 0 0;
  align-self: stretch;
  color: #8C9196;

  .no-games-icon {
    opacity: 0.6;
  }

  .no-games-text {
    color: var(---general-color-text-3, #8C9196);
    text-align: center;
    
    font-family: "PingFang SC";
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 17px;
  }
}

}

.skeleton-text {
  background-color: #d0d0d0;
  border-radius: 2px;
}
</style>
