const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const WebSocket = require('ws');
const { ipcMain, webContents } = require('electron');
const log = require('@heybox/electron-utils/log');
const { app } = require('electron');
const { requireAddon } = require('@heybox/electron-utils');
const toolHelper = requireAddon('tool_helper');

class SteamService {
  constructor() {
    this.jsContextServer = null;
    this.steamClient = null;
    this.isConnected = false;
    this.messageId = 0;
    this.ready = null;
    this.downloadMonitorInterval = null;
    this.lastDownloadCheck = null;

    this.BYTE_TO_MB_1024 = 1024 * 1024;
    this.BYTE_TO_MB_1000 = 1000 * 1000;

    this.initIpcHandlers();
  }



  initIpcHandlers() {
    // 启动 Steam 服务
    ipcMain.handle('steam:start-service', async () => {
      log.info('[SteamService] IPC: Starting Steam service...');
      return await this.startService();
    });

    // 停止 Steam 服务
    ipcMain.handle('steam:stop-service', async () => {
      log.info('[SteamService] IPC: Stopping Steam service...');
      return await this.stopService();
    });

    // 检查游戏安装状态
    ipcMain.handle('steam:check-game-status', async (event, appId) => {
      return await this.checkGameStatus(appId);
    });

    // 安装游戏
    ipcMain.handle('steam:install-game', async (event, appId, folderIdx) => {
      return await this.installGame(appId, folderIdx);
    });

    // 启动游戏
    ipcMain.handle('steam:run-game', async (event, appId) => {
      return await this.runGame(appId);
    });

    // 停止游戏
    ipcMain.handle('steam:terminate-game', async (event, appId) => {
      return await this.terminateGame(appId);
    });

    // 获取游戏信息
    ipcMain.handle('steam:get-app-info', async (event, appId) => {
      return await this.getAppInfo(appId);
    });

    // 获取所有应用信息
    ipcMain.handle('steam:get-apps-info', async (event) => {
      return await this.getAppsInfo();
    });

    // 获取下载概览
    ipcMain.handle('steam:get-download-overview', async () => {
      return await this.getDownloadOverview();
    });

    // 调试：获取 Steam 路径
    ipcMain.handle('steam:get-steam-path', async () => {
      const steamPath = await this.getSteamPath();
      return { success: true, steamPath };
    });

    // 添加快捷方式
    ipcMain.handle('steam:add-shortcut', async (event, options) => {
      return await this.addShortcut(options);
    });

    // 移除快捷方式
    ipcMain.handle('steam:remove-shortcut', async (event, appId) => {
      return await this.removeShortcut(appId);
    });

    // 创建桌面快捷方式
    ipcMain.handle('steam:create-desktop-shortcut', async (event, appId) => {
      return await this.createDesktopShortcut(appId);
    });

    // 调试：检查 Steam 状态
    ipcMain.handle('steam:check-steam-status', async () => {
      const steamPath = await this.getSteamPath();
      const steamRunning = await this.isSteamRunning();
      const debugFileExists = steamPath ? fs.existsSync(path.join(steamPath, '.cef-enable-remote-debugging')) : false;

      return {
        success: true,
        steamPath,
        steamRunning,
        debugFileExists,
        isConnected: this.isConnected
      };
    });

  }

  /**
   * 检查游戏是否为更新
   * update_state 字段：'Updating'=更新，'Downloading'=安装
   */
  checkIsUpdate(downloadData) {
    if (downloadData && downloadData.update_state) {
      // update_state: 'Updating' 表示更新，'Downloading' 表示安装
      return downloadData.update_state === 'Updating';
    }
    return false; // 默认为安装
  }

  /**
   * 计算下载和安装进度
   */
  calculateProgress(progressData) {
    let downloadProgress = 0;
    let installProgress = 0;
    let downloadMB = 0;
    let totalMB = 0;
    let overallProgress = 0;
    let progressText = '';

    if (progressData && progressData.length > 2) {
      // 正在下载数据的进度
      const downloadData = progressData[2];
      if (downloadData && downloadData.bytes_total > 0) {
        downloadProgress = Math.floor((downloadData.bytes_in_progress / downloadData.bytes_total) * 100);
        downloadMB = Math.round(downloadData.bytes_in_progress / this.BYTE_TO_MB_1024 * 10) / 10;
        totalMB = Math.round(downloadData.bytes_total / this.BYTE_TO_MB_1024 * 10) / 10;
      }

      // 正在安装文件的进度
      if (progressData.length > 3) {
        const installData = progressData[3];
        if (installData && installData.bytes_total > 0) {
          installProgress = Math.floor((installData.bytes_in_progress / installData.bytes_total) * 100);
        }
      }

      // 计算总体进度 使用安装进度作为主要显示
      if (installProgress > 0) {
        overallProgress = installProgress;
        progressText = `安装中`;
      } else if (downloadProgress > 0) {
        overallProgress = downloadProgress;
        progressText = `准备安装: ${downloadMB}MB / ${totalMB}MB`;
      } else {
        overallProgress = 0;
        progressText = `准备中`;
      }
    }

    return {
      downloadProgress,
      installProgress,
      downloadMB,
      totalMB,
      overallProgress,
      progressText
    };
  }

  /**
   * 处理活跃下载状态
   */
  async processActiveDownload(appId, networkSpeed, diskSpeed, isUpload, downloadData, additionalData = {}) {
    const isUpdate = this.checkIsUpdate(downloadData);
    const progress = this.calculateProgress(downloadData.progress);

    // 构造下载状态数据
    const downloadStatus = {
      appid: appId,
      network_speed: networkSpeed,
      disk_speed: diskSpeed,
      is_upload: isUpload,
      is_update: isUpdate,
      downloading: true,
      paused: false,
      progress: progress.overallProgress,
      progress_text: progress.progressText,
      download_progress: progress.downloadProgress,
      install_progress: progress.installProgress,
      download_mb: progress.downloadMB,
      total_mb: progress.totalMB,
      ...additionalData
    };

    // 发送实时下载状态到渲染进程
    this.broadcastDownloadStatus(downloadStatus);

    const logParts = [
      `[Steam下载] 应用: ${appId}`,
      `网络: ${networkSpeed}MB/s`,
      `磁盘: ${diskSpeed}MB/s`,
      `类型: ${isUpdate ? '更新' : '安装'}`,
      `上传: ${isUpload ? '是' : '否'}`,
      `进度: ${progress.overallProgress}% (${progress.progressText})`,
      `[下载: ${progress.downloadProgress}% 安装: ${progress.installProgress}%]`
    ];

    if (additionalData.peak_speed !== undefined) {
      logParts.splice(3, 0, `峰值: ${additionalData.peak_speed}MB/s`);
    }

    log.info(logParts.join(' '));
  }

  async startService() {
    try {
      if (this.isConnected || this.jsContextServer) {
        return { success: true, message: 'Service already running' };
      }

      await this.killExistingJSContextServers();

      const debugSetupResult = await this.setupSteamRemoteDebugging();
      if (!debugSetupResult.success) {
        return debugSetupResult;
      }

      // 启动 JSContextServer
      await this.startJSContextServer();

      // 连接到 JSContextServer
      try {
        await this.connectToJSContextServer();
        log.info('[SteamService] Service started successfully with JSContextServer connection');

        // 启动下载监控
        this.startDownloadMonitor();

        return { success: true, message: 'Service started successfully with JSContextServer connection' };
      } catch (connectionError) {
        log.warn('[SteamService] JSContextServer connection failed:', connectionError.message);
        return {
          success: false,
          message: 'JSContextServer connection failed',
          jsContextConnectionFailed: true,
          connectionError: connectionError.message
        };
      }
    } catch (error) {
      log.error('[SteamService] Failed to start service:', error);
      return { success: false, error: error.message };
    }
  }

  async stopService() {
    try {
      this.stopDownloadMonitor();

      if (this.steamClient) {
        this.steamClient.close();
        this.steamClient = null;
      }

      if (this.jsContextServer) {
        this.jsContextServer.kill();
        this.jsContextServer = null;
      }

      this.isConnected = false;

      log.info('[SteamService] Service stopped successfully');
      return { success: true, message: 'Service stopped successfully' };
    } catch (error) {
      log.error('[SteamService] Failed to stop service:', error);
      return { success: false, error: error.message };
    }
  }

  async setupSteamRemoteDebugging() {
    // exe 启动前要检测 steam 路径下（eg. C:\Program Files (x86)\Steam\.cef-enable-remote-debugging）是否有 .cef-enable-remote-debugging 文件
    // - 存在，启动 steam
    // - 不存在，创建文件名为 .cef-enable-remote-debugging 的空文件后再启动 steam，如果steam已经启动，创建文件后需要重启 steam
    try {
      log.info('[SteamService] Setting up Steam remote debugging...');

      // 获取 Steam 安装路径
      const steamPath = await this.getSteamPath();

      if (!steamPath) {
        log.error('[SteamService] Steam installation not found');
        return { success: false, error: 'Steam installation not found' };
      }

      const debugFilePath = path.join(steamPath, '.cef-enable-remote-debugging');
      log.info(`[SteamService] Debug file path: ${debugFilePath}`);

      // 检查文件是否存在
      const fileExists = fs.existsSync(debugFilePath);

      if (!fileExists) {
        // 创建空的调试文件
        try {
          fs.writeFileSync(debugFilePath, '');
          log.info('[SteamService] Created .cef-enable-remote-debugging file successfully');
        } catch (error) {
          log.error('[SteamService] Failed to create debug file:', error);
          return { success: false, error: 'Failed to create Steam debug file' };
        }

        // 检查 Steam 是否正在运行
        const steamRunning = await this.isSteamRunning();
        log.info(`[SteamService] Steam running status: ${steamRunning}`);

        if (steamRunning) {
          log.warn('[SteamService] Steam is running, attempting automatic restart for remote debugging');

          const restartResult = await this.restartSteamUsingToolHelper();
          if (!restartResult.success) {
            log.error('[SteamService] Failed to restart Steam automatically:', restartResult.error);
            return {
              success: false,
              error: 'Steam restart required. Please restart Steam manually and try again.',
              requiresSteamRestart: true,
              autoRestartFailed: true,
              restartError: restartResult.error
            };
          }

          log.info('[SteamService] Steam restarted successfully');
        }
      }

      // 检查 Steam CEF 调试端口是否可用
      const debugPortAvailable = await this.checkSteamDebugPort();
      if (!debugPortAvailable) {
        log.warn('[SteamService] Steam CEF debug port (8080) is not accessible - Steam may need to be restarted');
      } else {
        log.info('[SteamService] Steam CEF debug port is accessible');

        // 获取 SharedJSContext 信息
        const sharedJSContext = await this.findSharedJSContext();
        if (sharedJSContext) {
          log.info(`[SteamService] Found SharedJSContext: ${sharedJSContext.webSocketDebuggerUrl}`);
        } else {
          log.warn('[SteamService] SharedJSContext not found in debug targets');
        }
      }

      log.info('[SteamService] Steam remote debugging setup completed successfully');
      return { success: true };
    } catch (error) {
      log.error('[SteamService] Failed to setup Steam remote debugging:', error);
      return { success: false, error: error.message };
    }
  }

  async getSteamPath() {
    // 从注册表获取 Steam 路径
    try {
      const { getRegeditKey } = require('@heybox/electron-utils');
      const steamPath = await getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath');
      log.info(`[SteamService] Registry returned: ${steamPath}`);
      return steamPath;
    } catch (error) {
      log.warn('[SteamService] Failed to get Steam path from registry:', error);
    }
  }

  async isSteamRunning() {
    try {
      const { exec } = require('child_process');
      return new Promise((resolve) => {
        exec('tasklist /FI "IMAGENAME eq steam.exe"', (error, stdout) => {
          if (error) {
            resolve(false);
            return;
          }
          resolve(stdout.toLowerCase().includes('steam.exe'));
        });
      });
    } catch (error) {
      log.error('[SteamService] Error checking if Steam is running:', error);
      return false;
    }
  }

  async killExistingJSContextServers() {
    try {
      const { exec } = require('child_process');
      return new Promise((resolve) => {
        exec('taskkill /F /IM JSContextServer.exe', (error) => {
          if (error) {
            // 正常情况下找不到
            log.info('[SteamService] No existing JSContextServer processes found');
          } else {
            log.info('[SteamService] Killed existing JSContextServer processes');
          }
          resolve();
        });
      });
    } catch (error) {
      log.warn('[SteamService] Error killing existing JSContextServer processes:', error);
    }
  }

  async checkSteamDebugPort() {
    try {
      const http = require('http');
      return new Promise((resolve) => {
        const req = http.get('http://localhost:8080', () => {
          resolve(true);
        });

        req.on('error', () => {
          resolve(false);
        });

        req.setTimeout(3000, () => {
          req.destroy();
          resolve(false);
        });
      });
    } catch (error) {
      log.error('[SteamService] Error checking Steam debug port:', error);
      return false;
    }
  }

  async findSharedJSContext() {
    try {
      const http = require('http');
      return new Promise((resolve) => {
        const req = http.get('http://localhost:8080/json', (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const targets = JSON.parse(data);
              const sharedJSContext = targets.find(target =>
                target.title === 'SharedJSContext' && target.webSocketDebuggerUrl
              );
              resolve(sharedJSContext || null);
            } catch (parseError) {
              log.error('[SteamService] Failed to parse debug targets JSON:', parseError);
              resolve(null);
            }
          });
        });

        req.on('error', (error) => {
          log.error('[SteamService] Error fetching debug targets:', error);
          resolve(null);
        });

        req.setTimeout(5000, () => {
          req.destroy();
          resolve(null);
        });
      });
    } catch (error) {
      log.error('[SteamService] Error finding SharedJSContext:', error);
      return null;
    }
  }

  async connectToJSContextServer() {
    return new Promise((resolve, reject) => {
      const jsContextServerUrl = 'ws://localhost:7355';
      log.info(`[SteamService] Connecting to JSContextServer: ${jsContextServerUrl}`);

      this.steamClient = new WebSocket(jsContextServerUrl);

      this.ready = new Promise((resolveReady) => {
        this.steamClient.addEventListener("open", () => resolveReady());
      });

      const connectionTimeout = setTimeout(() => {
        if (!this.isConnected) {
          log.error('[SteamService] Connection to JSContextServer timeout');
          if (this.steamClient) {
            this.steamClient.terminate();
            this.steamClient = null;
          }
          reject(new Error('Connection to JSContextServer timeout'));
        }
      }, 10000);

      this.steamClient.addEventListener('open', () => {
        log.info('[SteamService] Connected to JSContextServer successfully');
        this.isConnected = true;
        clearTimeout(connectionTimeout);
        resolve();
      });

      this.steamClient.addEventListener('close', (event) => {
        log.info(`[SteamService] JSContextServer connection closed: ${event.code} - ${event.reason}`);
        this.steamClient = null;
        this.isConnected = false;

        this.stopDownloadMonitor();
      });

      this.steamClient.addEventListener('error', (error) => {
        log.error('[SteamService] JSContextServer connection error:', error);
        clearTimeout(connectionTimeout);
        this.steamClient = null;
        this.isConnected = false;
        reject(error);
      });
    });
  }

  async startJSContextServer() {
    return new Promise((resolve, reject) => {
      let jsContextPath, payloadPath;
      if (app.isPackaged) {
        // 打包环境中，extraResources 将文件复制到 resources/utils/installProcess/
        const appResourcesPath = path.join(__dirname, '../../../');
        jsContextPath = path.join(appResourcesPath, 'utils/installProcess/JSContextServer.exe');
        payloadPath = path.join(appResourcesPath, 'utils/installProcess/payload.inject.js');
      } else {
        jsContextPath = path.join(__dirname, '../utils/installProcess/JSContextServer.exe');
        payloadPath = path.join(__dirname, '../utils/installProcess/payload.inject.js');
      }
      
      if (!fs.existsSync(jsContextPath)) {
        reject(new Error('JSContextServer.exe not found'));
        return;
      }

      if (!fs.existsSync(payloadPath)) {
        reject(new Error('payload.inject.js not found'));
        return;
      }

      let isResolved = false;

      // 启动 JSContextServer
      this.jsContextServer = spawn(jsContextPath, [], {
        stdio: 'pipe',
        cwd: path.dirname(jsContextPath)
      });

      this.jsContextServer.stdout.on('data', (data) => {
        const output = data.toString().trim();
        log.info(`[JSContextServer] stdout ${output}`);
        if (output.endsWith('JSContextServer initialized!') && !isResolved) {
          isResolved = true;
          log.info('[SteamService] JSContextServer initialization completed');
          resolve();
        }
      });

      this.jsContextServer.stderr.on('data', (data) => {
        const error = data.toString().trim();
        log.info(`[JSContextServer] stderr ${error}`);
      });

      this.jsContextServer.on('close', (code) => {
        log.info(`[JSContextServer] Process exited with code ${code}`);
        this.jsContextServer = null;

        if (code !== 0) {
          log.error(`[JSContextServer] Process crashed with exit code ${code}`);
          if (!isResolved) {
            isResolved = true;
            reject(new Error(`JSContextServer process crashed with exit code ${code}`));
          }
        }
      });

      this.jsContextServer.on('error', (error) => {
        log.error('[SteamService] Process error:', error);
        if (!isResolved) {
          isResolved = true;
          reject(error);
        }
      });

      setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          log.warn('[SteamService] JSContextServer initialization timeout');
          resolve();
        }
      }, 30000);
    });
  }

  /**
   * 启动下载监控
   */
  startDownloadMonitor() {
    if (this.downloadMonitorInterval) {
      clearInterval(this.downloadMonitorInterval);
    }

    this.checkDownloadStatus();

    // 每秒检查一次下载状态
    this.downloadMonitorInterval = setInterval(() => {
      this.checkDownloadStatus();
    }, 1000);
  }

  /**
   * 停止下载监控
   */
  stopDownloadMonitor() {
    if (this.downloadMonitorInterval) {
      clearInterval(this.downloadMonitorInterval);
      this.downloadMonitorInterval = null;
      log.info('[SteamService] Download monitor stopped');
    }
  }

  /**
   * 检查下载状态 log
   */
  async checkDownloadStatus() {
    if (!this.isConnected) return;

    try {
      const res = await this.sendCommand('GetDownloadOverview', {});

      if (!res || typeof res !== 'object') return;

      if (res.history && Array.isArray(res.history) && res.history.length > 0) {
        const latestDownload = res.history[res.history.length - 1];

        if (latestDownload.network_bytes_per_second > 0 || latestDownload.disc_bytes_per_second > 0) {
          const networkSpeed = Math.round(latestDownload.network_bytes_per_second / this.BYTE_TO_MB_1000 * 10) / 10;
          const diskSpeed = Math.round(latestDownload.disc_bytes_per_second / this.BYTE_TO_MB_1000 * 10) / 10;

          await this.processActiveDownload(
            latestDownload.appid,
            networkSpeed,
            diskSpeed,
            latestDownload.is_upload,
            res
          );
        } else {
          // 没有活跃下载
          this.broadcastDownloadStatus({ downloading: false, paused: false });
        }
      }
    } catch (error) {
      if (error.message !== 'JSContextServer not connected') {
        log.debug('[SteamService] Download status check failed:', error.message);
      }
    }
  }

  async sendCommand(command, args = {}) {
    if (!this.isConnected || !this.steamClient) {
      throw new Error('JSContextServer not connected');
    }

    // 等待连接就绪
    await this.ready;

    const messageId = this.messageId;
    this.messageId++;
    if (this.messageId >= 500) {
      this.messageId = 0; 
    }

    const message = {
      secret: "Secret!",
      messageId,
      command,
      args
    };

    log.info(`[SteamService] ${command} with messageId: ${messageId}`);

    return new Promise((resolve, reject) => {
      // 设置消息监听器
      let listener = (event) => {
        let data = JSON.parse(event.data);
        if (data.hasOwnProperty("messageId") && data.messageId === messageId) {
          if (this.steamClient) {
            this.steamClient.removeEventListener("message", listener);
          }
          resolve(data);
        }
      };

      this.steamClient.addEventListener("message", listener);
      this.steamClient.send(JSON.stringify(message));

      // 设置超时
      setTimeout(() => {
        if (this.steamClient) {
          this.steamClient.removeEventListener("message", listener);
        }
        reject(new Error(`Request timeout for command: ${command}`));
      }, 10000);
    });
  }

  async checkGameStatus(appId) {
    try {
      const response = await this.sendCommand('GetAppInfo', { appId: parseInt(appId) });
      if (response.success) {
        return {
          success: true,
          installed: response.installed,
          status: response.status,
          displayName: response.displayName
        };
      }
      return { success: false, error: response.error };
    } catch (error) {
      log.error('[SteamService] Failed to check game status:', error);
      return { success: false, error: error.message };
    }
  }

  async installGame(appId, folderIdx) {
    try {
      const response = await this.sendCommand('InstallApp', { 
        appId: parseInt(appId),
        folderIdx: folderIdx ? parseInt(folderIdx) : undefined
      });
      return response;
    } catch (error) {
      log.error('[SteamService] Failed to install game:', error);
      return { success: false, error: error.message };
    }
  }

  async runGame(appId) {
    try {
      const response = await this.sendCommand('RunApp', { appId: parseInt(appId) });
      return response;
    } catch (error) {
      log.error('[SteamService] Failed to run game:', error);
      return { success: false, error: error.message };
    }
  }

  async terminateGame(appId) {
    try {
      const response = await this.sendCommand('TerminateApp', { appId: parseInt(appId) });
      return response;
    } catch (error) {
      log.error('[SteamService] Failed to terminate game:', error);
      return { success: false, error: error.message };
    }
  }

  async addShortcut(options) {
    try {
      const { name, exe, launchOptions = [], icon, startDir } = options;

      if (!exe) {
        return { success: false, error: 'Missing required parameter: exe' };
      }

      const response = await this.sendCommand('AddShortcut', {
        name: name || '',
        exe,
        launchOptions,
        icon,
        startDir
      });

      return response;
    } catch (error) {
      log.error('[SteamService] Failed to add shortcut:', error);
      return { success: false, error: error.message };
    }
  }

  async removeShortcut(appId) {
    try {
      if (!appId) {
        return { success: false, error: 'Missing required parameter: appId' };
      }

      const response = await this.sendCommand('RemoveShortcut', {
        appId: parseInt(appId)
      });

      return response;
    } catch (error) {
      log.error('[SteamService] Failed to remove shortcut:', error);
      return { success: false, error: error.message };
    }
  }

  async createDesktopShortcut(appId) {
    try {
      if (!appId) {
        return { success: false, error: 'Missing required parameter: appId' };
      }

      const response = await this.sendCommand('CreateDesktopShortcutForApp', {
        appId: parseInt(appId)
      });

      return response;
    } catch (error) {
      log.error('[SteamService] Failed to create desktop shortcut:', error);
      return { success: false, error: error.message };
    }
  }

  async getAppInfo(appId) {
    try {
      const response = await this.sendCommand('GetAppInfo', { appId: parseInt(appId) });
      return response;
    } catch (error) {
      log.error('[SteamService] Failed to get app info:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取所有应用信息
   * @returns {Promise<Object>} 所有应用信息
   */
  async getAppsInfo() {
    try {
      // 获取所有应用ID
      const appsResponse = await this.sendCommand('GetApps', {
        typeFilter: [1, 2] // 1=Game, 2=Shortcut
      });

      if (!appsResponse.success) {
        return { success: false, error: appsResponse.error || 'Failed to get apps' };
      }

      // 分批获取应用详细信息
      const appIds = appsResponse.appIds || [];
      const chunkSize = 100;
      const chunkCount = Math.ceil(appIds.length / chunkSize);
      const apps = [];

      log.info(`[SteamService] Fetching info for ${appIds.length} apps in ${chunkCount} chunks`);

      const startTime = Date.now();

      // 分批并行请求
      for (let i = 0; i < chunkCount; i++) {
        const chunk = appIds.slice(i * chunkSize, (i + 1) * chunkSize);
        const chunkPromises = chunk.map(appId =>
          this.sendCommand('GetAppInfo', { appId: parseInt(appId) })
            .then(app => {
              if (app.success) {
                // 添加ID到返回对象
                app.id = parseInt(appId);
                apps.push(app);
              }
              return app;
            })
            .catch(error => {
              log.error(`[SteamService] Failed to get info for app ${appId}:`, error);
              return null;
            })
        );

        await Promise.all(chunkPromises);
        log.info(`[SteamService] Processed chunk ${i+1}/${chunkCount}`);
      }

      const duration = Date.now() - startTime;
      log.info(`[SteamService] Fetched info for ${apps.length} apps in ${duration}ms`);

      return {
        success: true,
        apps,
        count: apps.length,
        duration
      };
    } catch (error) {
      log.error('[SteamService] Failed to get apps info:', error);
      return { success: false, error: error.message };
    }
  }

  async getDownloadOverview() {
    try {
      const response = await this.sendCommand('GetDownloadOverview');
      return response;
    } catch (error) {
      log.error('[SteamService] Failed to get download overview:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 广播下载状态到渲染进程
   */
  broadcastDownloadStatus(downloadStatus) {
    try {
      const allWebContents = webContents.getAllWebContents();

      allWebContents.forEach(contents => {
        if (!contents.isDestroyed()) {
          contents.send('steam:download-status-update', downloadStatus);
        }
      });
    } catch (error) {
      log.error('[SteamService] 广播下载状态失败:', error);
    }
  }

  /**
   * 使用 tool_helper addon 重启 Steam
   */
  async restartSteamUsingToolHelper() {
    try {
      return new Promise((resolve) => {
        toolHelper.switchSteamAccount((error) => {
          if (error) {
            log.error('[SteamService] Failed to restart Steam using tool_helper:', error);
            resolve({ success: false, error: error.toString() });
          } else {
            resolve({ success: true });
          }
        }, {
          accountName: '', 
          arguments: "-silent"
        });
      });
    } catch (error) {
      log.error('[SteamService] Error restarting Steam using tool_helper:', error);
      return { success: false, error: error.message };
    }
  }
}

module.exports = SteamService;
