<template>
  <div 
    class="cpt-virtual-list" 
    @scroll="handleScroll"
  >
    <slot name="top" ref="topSlotRef"></slot>
    <div class="virtual-list-container"
      ref="containerRef"
      
    >
      <!-- 顶部占位符，保持滚动位置 -->
      <div 
        v-if="topPlaceholderHeight > 0" 
        :style="{ height: `${topPlaceholderHeight}px` }"
        class="placeholder"
      ></div>
      
      <!-- 可见的项目 -->
      <div 
        v-for="item in visibleItems" 
        :key="getItemKey(item)"
        :style="getItemStyle(item)"
        class="virtual-list-item"
      >
      <slot :item="item" :index="getItemIndex(item)"></slot>
      </div>
      
      <!-- 底部占位符，保持滚动位置 -->
      <div 
        v-if="bottomPlaceholderHeight > 0" 
        :style="{ height: `${bottomPlaceholderHeight}px` }"
        class="placeholder"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { throttle } from '@heybox-app-web-shared/utils';

const props = defineProps({
  // 数据列表
  items: {
    type: Array,
    default: () => []
  },
  // 每行显示的项目数量
  itemsPerRow: {
    type: Number,
    default: 1
  },
  // 项目高度
  itemHeight: {
    type: Number,
    required: true
  },
  // 行间距
  rowGap: {
    type: Number,
    default: 16
  },
  // 缓冲区大小（行数）
  bufferRows: {
    type: Number,
    default: 2
  },
  // 获取项目key的函数
  getItemKey: {
    type: Function,
    default: (item) => item.id || item.appid || item.key
  },
  // 获取项目索引的函数
  getItemIndex: {
    type: Function,
    default: (item) => item.originalIndex || 0
  },
  // 滚动到底部的回调
  onReachBottom: {
    type: Function,
    default: null
  },
  // 滚动节流时间
  throttleTime: {
    type: Number,
    default: 100
  }
});

const emit = defineEmits(['scroll']);

const containerRef = ref(null);
const scrollTop = ref(0);
const viewportHeight = ref(0);

// 计算行高（项目高度 + 行间距）
const rowHeight = computed(() => {
  return props.itemHeight + props.rowGap;
});

// 虚拟滚动：只渲染可见的项目
const visibleItems = computed(() => {
  if (!props.items.length) return [];
  
  // 计算当前滚动位置对应的行数
  const currentRow = Math.floor(scrollTop.value / rowHeight.value);
  
  // 计算可见区域的行数
  const visibleRows = Math.ceil(viewportHeight.value / rowHeight.value);
  
  // 计算开始和结束的行数
  const startRow = Math.max(0, currentRow - props.bufferRows);
  const endRow = Math.min(
    Math.ceil(props.items.length / props.itemsPerRow),
    currentRow + visibleRows + props.bufferRows
  );
  
  // 计算对应的项目索引
  const startIndex = startRow * props.itemsPerRow;
  const endIndex = Math.min(props.items.length, endRow * props.itemsPerRow);
  
  // 返回带有原始索引的项目数组
  return props.items.slice(startIndex, endIndex).map((item, index) => ({
    ...item,
    originalIndex: startIndex + index
  }));
});

// 计算总内容高度
const totalContentHeight = computed(() => {
  if (!props.items.length) return 0;
  
  const totalRows = Math.ceil(props.items.length / props.itemsPerRow);
  return totalRows * rowHeight.value;
});

// 计算顶部占位符高度
const topPlaceholderHeight = computed(() => {
  const currentRow = Math.floor(scrollTop.value / rowHeight.value);
  const startRow = Math.max(0, currentRow - props.bufferRows);
  
  return startRow * rowHeight.value;
});

// 计算底部占位符高度
const bottomPlaceholderHeight = computed(() => {
  const currentRow = Math.floor(scrollTop.value / rowHeight.value);
  const visibleRows = Math.ceil(viewportHeight.value / rowHeight.value);
  const endRow = Math.min(
    Math.ceil(props.items.length / props.itemsPerRow),
    currentRow + visibleRows + props.bufferRows
  );
  
  const totalRows = Math.ceil(props.items.length / props.itemsPerRow);
  return (totalRows - endRow) * rowHeight.value;
});

// 获取项目样式
const getItemStyle = (item) => {
  return {
    height: `${props.itemHeight}px`,
    flexShrink: 0
  };
};

// 滚动处理函数
const handleScroll = throttle(() => {
  if (containerRef.value) {
    scrollTop.value = containerRef.value.scrollTop;
    viewportHeight.value = containerRef.value.clientHeight;

    // 触发滚动事件
    emit('scroll', {
      scrollTop: scrollTop.value,
      viewportHeight: viewportHeight.value
    });

    // 检查是否接近底部
    if (props.onReachBottom) {
      const totalRows = Math.ceil(props.items.length / props.itemsPerRow);
      const currentRow = Math.floor(scrollTop.value / rowHeight.value);
      
      // 当滚动到最后几行时触发加载
      const triggerRow = totalRows - Math.ceil(viewportHeight.value / rowHeight.value) - 2;
      if (currentRow >= triggerRow) {
        props.onReachBottom();
      }
    }
  }
}, props.throttleTime);

// 监听容器大小变化
let resizeObserver = null;

const handleResize = throttle(() => {
  if (containerRef.value) {
    viewportHeight.value = containerRef.value.clientHeight;
  }
}, 10);

// 暴露方法给父组件
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0;
  }
};

const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = containerRef.value.scrollHeight;
  }
};

const getScrollInfo = () => {
  return {
    scrollTop: scrollTop.value,
    viewportHeight: viewportHeight.value,
    totalHeight: totalContentHeight.value
  };
};

// 暴露方法
defineExpose({
  scrollToTop,
  scrollToBottom,
  getScrollInfo
});

onMounted(async () => {
  await nextTick();
  
  if (containerRef.value) {
    // 初始化视口高度
    viewportHeight.value = containerRef.value.clientHeight;
    
    // 监听容器大小变化
    resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(containerRef.value);
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
});

// 监听items变化，重新计算
watch(() => props.items, () => {
  nextTick(() => {
    if (containerRef.value) {
      viewportHeight.value = containerRef.value.clientHeight;
    }
  });
}, { deep: true });
</script>

<style lang="scss">
.cpt-virtual-list {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  will-change: scroll-position;

  .virtual-list-container {
    height: auto;
    .placeholder {
      width: 100%;
      flex-shrink: 0;
    }
    
    .virtual-list-item {
      display: inline-block;
      vertical-align: top;
    }
  }
}

</style> 