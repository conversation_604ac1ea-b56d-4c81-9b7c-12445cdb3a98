<template>
  <div
    class="cpt-account-card"
    ref="accountCard"
    :class="{
      'show-all': showAll,
    }"
    :style="{
      width: showAll ? `${currentCardWidth}px` : `${cardConfig.width}px`,
    }"
  >
    <!-- 添加背景视频 -->
    <video
      v-if="account.background_video"
      class="background-video"
      autoplay
      muted
      loop
      playsinline
    >
      <source :src="account.background_video" type="video/mp4">
    </video>
    
    <!-- 保留原有的背景图片作为备用 -->
    <div
      v-if="!account.background_video && account.head_image"
      class="background-image"
      :style="{
        backgroundImage: `url(${account.head_image})`,
      }"
    ></div>

    <template v-if="showAll">
      <div class="main-info-row">
        <AccountCardAvatar
          :avatar="account.avatar"
          :decoration="account.avatar_frame"
          :showDecoration="true"
        />
        <div class="main-info-row-content">
          <div class="account-nickname">
            {{ account.nick_name || (account.online_state && account.online_state.personaname) }}
          </div>
          <div class="account-game-status">
            <!-- <div class="game-icon">
              <img
                :src="account.game_status?.game_icon"
                alt="game-icon"
              />
            </div> -->
            <div class="game-name" :class="{ 'online': isOnline }">{{ steamStatus }}</div>
          </div>
          <div class="account-intro">
            {{ account.signature }}
          </div>
        </div>
      </div>
      <div class="other-info-row">
        <div class="heybox-info">
          <HbLevel :level="account.level" :levelIcon="account.level_icon" />
          <div class="heybox-info-content">
            <div class="country-from">
              <span>国家：</span>
              <div class="country-from-icon">
                <img
                  :src="account.country_flag"
                  alt="country-icon"
                />
              </div>
            </div>
            <div class="hb-aged"><span>年限：</span>{{ account.account_year }}</div>
          </div>
        </div>
        <div class="account-info">
          <div v-for="item in account.game_overview" :key="item.key" class="block">
            <div class="top">
              <div class="top-value account">
                {{ item.value }}
                <div class="rank" :style="{ color: item.color }">{{ item.rank }}</div>
              </div>
            </div>
            <div class="label">{{ item.desc }}</div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="card-content">
        <AccountCardAvatar
          :avatar="account.avatar"
          :decoration="account.avatar_frame"
          :showDecoration="true"
        />
        <div class="card-info">
          <div class="account-nickname">
            {{ account.nick_name }}
          </div>
          <div class="account-add-time">
            {{ formatDate(Number(account.timestamp * 1000), 'YYYY.MM.DD') }}
          </div>
        </div>
      </div>
    </template>
    <Selector
      :options="menuOptions"
      @select="handleMenuSelect"
      type="menu"
      :showCheckIcon="false"
      placement="bottom-end"
    >
      <div class="more-btn">
        <i class="iconfont icon-common-more-line"></i>
      </div>
    </Selector>
  </div>
</template>

<script setup>
import AccountCardAvatar from '_com/func/Avatar.vue';
import HbLevel from '_com/func/HbLevel.vue';
import { formatDate } from '@heybox-app-web-shared/utils';
import { ref, defineProps, onMounted, onUnmounted, watch, computed, defineEmits } from 'vue';
import { CARD_GAP } from '_constant/index';
import { useEventBus } from '@heybox-app-web-shared/eventbus';

const emit = defineEmits(['clickCard']);
const { on } = useEventBus();
const accountCard = ref(null);
const steamStatus = ref('')
const isOnline = ref(false)

const props = defineProps({
  account: {
    type: Object,
    required: true,
  },
  showAll: {
    type: Boolean,
    default: false,
  },
  cardConfig: {
    type: Object,
    required: true,
  },
});

const menuOptions = ref([
  {
    label: '切换账号',
    value: 'switch',
    hidden: props.showAll,
  },
  { 
    label: '以离线模式启动',
    value: 'offline',
  },
  {
    label: '备注',
    value: 'remark',
  },
  {
    label: '移除账号',
    value: 'delete',
    type: 'danger',
    hidden: props.showAll,
  },
])

watch(() => props.showAll, () => {  
  menuOptions.value.forEach(item => {
    if(item.value === 'switch' || item.value === 'delete') {
      item.hidden = props.showAll
    }
  })
})

const currentCardWidth = computed(() => {
  if(props.cardConfig.cardsPerRow <= 4) {
    return props.cardConfig.width * 3 + CARD_GAP * 2
  } else if(props.cardConfig.cardsPerRow > 7) {
    return props.cardConfig.width * 5 + CARD_GAP * 4
  } else {
    return props.cardConfig.width * (props.cardConfig.cardsPerRow - 2) + CARD_GAP * (props.cardConfig.cardsPerRow - 3)
  }
})

onMounted(() => {
  // 获取当前的Element
  console.log(props.account)
  const el = accountCard.value;
  if (el && props.showAll) {
    el.style.setProperty(
      '--account-value-rank-range',
      props.account.account_value_rank_range + '%'
    );
    el.style.setProperty(
      '--playing-time-rank-range',
      props.account.playing_time_rank_range + '%'
    );
  }
  forceUpdate()
  if(props.showAll) {
    on('updateSteamStatus', forceUpdate)
  }
});

const forceUpdate = () => {
  computeIsOnline()
  computeSteamStatus()
}

const computeSteamStatus = () => {
  if(!isOnline.value) steamStatus.value = '离线'
  else {
    if(props.account.online_state && props.account.online_state.gameextrainfo) {
      steamStatus.value = `游戏中：${props.account.online_state.gameextrainfo}`
    } else {
      steamStatus.value = `在线`
    }
  }
}

const computeIsOnline = () => {
  isOnline.value = props.account.online_state && props.account.online_state.personastate === 1
}

const handleClick = () => {
  if(!props.showAll) {
    emit('clickCard', props.account)
  }
}

const handleMenuSelect = (option) => {
  if(option.value === 'switch') {
    emit('switchAccount', props.account)
  } else if(option.value === 'offline') {
    emit('offlineAccount', props.account)
  } else if(option.value === 'remark') {
    emit('remarkAccount', props.account)
  } else if(option.value === 'delete') {
    emit('deleteAccount', props.account)
  }
}
</script>

<style lang="scss">
.cpt-account-card {
  --account-value-rank-range: '0%';
  --playing-time-rank-range: '0%';

  display: flex;
  width: 151px;
  height: 170px;
  padding: 14px 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  border-radius: 8px;
  flex-shrink: 0;
  border: 1px solid var(---white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
  &::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    width: 100%;
    height: 100%;
    border-radius: inherit;
    border: 1px solid
      var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
  }

  position: relative;
  overflow: hidden; // 确保视频不会超出边界

  &.show-all {
    flex-shrink: 0;
    padding: 14px 16px;
  }

  // 背景视频样式
  .background-video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
    border-radius: inherit;
  }

  // 背景图片样式
  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 0;
    border-radius: inherit;
  }

  // 添加一个半透明遮罩层，确保内容可读性
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
    border-radius: inherit;
  }

  .card-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    position: relative;
    z-index: 1;
    .card-info {
      display: flex;
      height: 58px;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      gap: 4px;
      align-self: stretch;
      .account-nickname {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        flex: 1 0 0;
        align-self: stretch;
        color: #fff;
        font-size: 14px;
        font-weight: 600;
        line-height: normal;
        white-space: pre-wrap;
      }
      .account-add-time {
        align-self: stretch;
        color: var(
          --white-gamerecord-color-white-70a,
          rgba(255, 255, 255, 0.7)
        );
        font-size: 10px;
        line-height: normal;
      }
    }
  }
  .cpt-selector {
    position: absolute;
    right: 4px;
    top: 4px;
    z-index: 1;
  }
  .more-btn {
    padding: 4px;
    line-height: 14px;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
      border-radius: 5px;
    }
    .iconfont {
      font-size: 14px;
      color: #FFF;
    }
  }

  .main-info-row {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;

    .main-info-row-content {
      display: flex;
      width: 0;
      flex: 1;
      height: 62px;
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      .account-nickname {
        align-self: stretch;
        color: #fff;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .account-game-status {
        display: flex;
        align-items: center;
        gap: 4px;
        .game-icon {
          width: 14px;
          height: 14px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .game-name {
          color: $general-color-text-3;
          font-size: 12px;
          line-height: 16px;
          &.online {
            color: #32b846;
          }
        }
      }
      .account-intro {
        align-self: stretch;
        color: var(
          --white-gamerecord-color-white-70a,
          rgba(255, 255, 255, 0.7)
        );
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px;
      }
    }
  }

  .other-info-row {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
    .heybox-info {
      display: flex;
      align-items: center;
      gap: 8px;
      display: flex;
      height: 58px;
      padding: 12px 10px;
      gap: 10px;
      border-radius: 8px;
      border: 1px solid
        var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      background-color: var(
        --white-gamerecord-color-white-05a,
        rgba(255, 255, 255, 0.05)
      );
      .heybox-info-content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        .country-from {
          display: flex;
          align-items: center;
          gap: 4px;
          color: var(
            --white-gamerecord-color-white-50a,
            rgba(255, 255, 255, 0.5)
          );
          font-size: 10px;
          line-height: normal;
          .country-from-icon {
            width: 18px;
            height: 11px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
        .hb-aged {
          color: var(
            --white-gamerecord-color-white-50a,
            rgba(255, 255, 255, 0.5)
          );
          font-size: 10px;
          line-height: normal;
        }
      }
    }
    .account-info {
      display: flex;
      width: 0;
      flex: 1;
      height: 58px;
      padding: 14px 10px;
      align-items: center;
      border-radius: 8px;
      border: 1px solid
        var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      background-color: var(
        --white-gamerecord-color-white-05a,
        rgba(255, 255, 255, 0.05)
      );
      .block {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
        height: 36px;
        .top {
          width: 100%;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          .top-value {
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            position: relative;
            .rank {
              position: absolute;
              bottom: 0;
              right: 0;
              font-size: 10px;
              transform: translateX(calc(100% + 1px));
            }
          }
          
        }
        .label {
          color: var(
            --white-gamerecord-color-white-50a,
            rgba(255, 255, 255, 0.5)
          );
          text-align: center;
          font-size: 10px;
          font-weight: 400;
        }
      }
    }
  }
}
</style>
