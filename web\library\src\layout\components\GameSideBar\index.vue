<template>
  <div class="cpt-game-sidebar">
    <div class="header-row">
      <div
        class="header-name"
        @click="handleBackHome"
      >
        游戏库
      </div>
      <div
        class="header-add-btn"
      >
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_492_142)">
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M9.9001 1.87461C9.9001 1.37755 9.49715 0.974609 9.0001 0.974609C8.50304 0.974609 8.1001 1.37755 8.1001 1.87461V8.09961H1.8751C1.37804 8.09961 0.975098 8.50255 0.975098 8.99961C0.975098 9.49667 1.37804 9.89961 1.8751 9.89961H8.1001V16.1246C8.1001 16.6217 8.50304 17.0246 9.0001 17.0246C9.49715 17.0246 9.9001 16.6217 9.9001 16.1246V9.89961H16.1251C16.6222 9.89961 17.0251 9.49667 17.0251 8.99961C17.0251 8.50255 16.6222 8.09961 16.1251 8.09961H9.9001V1.87461Z"
              fill="#8C9196"
            />
          </g>
          <defs>
            <clipPath id="clip0_492_142">
              <rect
                width="18"
                height="18"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      </div>
    </div>
    <div class="search-wrapper">
      <div class="search-input-wrapper">
        <div class="search-input">
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M2.5 6C2.5 4.067 4.067 2.5 6 2.5C7.933 2.5 9.5 4.067 9.5 6C9.5 7.933 7.933 9.5 6 9.5C4.067 9.5 2.5 7.933 2.5 6ZM6 1C3.23858 1 1 3.23858 1 6C1 8.76142 3.23858 11 6 11C7.11016 11 8.13582 10.6382 8.96549 10.0261L10.2203 11.2808C10.5132 11.5737 10.9878 11.5735 11.2804 11.2803C11.573 10.9872 11.5728 10.5121 11.2799 10.2192L10.0261 8.96538C10.6382 8.13573 11 7.11011 11 6C11 3.23858 8.76142 1 6 1Z"
              fill="#8C9196"
            />
          </svg>

          <input
            type="text"
            v-model="searchStr"
            placeholder="搜索游戏"
          />
        </div>
      </div>
      <div class="filter-container">
        <Selector
          class="filter-option"
          v-model="currentOrder"
          :options="orderPopoverConfigs"
          placement="bottom-start"
          @select="handleChangeOrder"
          :disable="!!searchStr"
        >
          <div class="filter-option-label" :class="{'disabled': searchStr}">
            <i class="iconfont icon-common-filter2-filled"></i>
            <span>{{ currentOrderLabel }}</span>
          </div>
        </Selector>
        <div class="vertical-split-line"></div>
        <div
          class="filter-list-btn"
          ref="addGamePopoverTriggerRef"
        >
          <i class="iconfont icon-common-filter-filled"></i>
          <span>筛选</span>
        </div>
      </div>
    </div>
    <div class="divider"></div>
    <template v-if="filterKeys.length > 0">
      <div class="filter-list-container">
        <div class="filter-list">
          <div class="filter-list-item" v-for="item in filterKeys" :key="item.key">
            {{ item.desc }}
            <i class="iconfont icon-common-close-line" @click="handleRemoveFilter(item)"></i>
          </div>
        </div>
        <div class="reset-btn" @click="handleResetForm">
          重置
        </div>
      </div>
      <div class="divider"></div>
    </template>
    <!-- 游戏列表区域：存在骨架屏 -->
    <template v-if="!isSteamInstalled">
      <div class="no-games-placeholder">
        <div class="no-games-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.00004 4C4.52728 4 3.33337 5.19391 3.33337 6.66667V10.6667C3.33337 12.1394 4.52728 13.3333 6.00004 13.3333H11.3334C12.8061 13.3333 14 12.1394 14 10.6667V6.66667C14 5.19391 12.8061 4 11.3334 4H6.00004ZM6.00004 18.6667C4.52728 18.6667 3.33337 19.8606 3.33337 21.3333V25.3333C3.33337 26.8061 4.52728 28 6.00004 28H11.3334C12.8061 28 14 26.8061 14 25.3333V21.3333C14 19.8606 12.8061 18.6667 11.3334 18.6667H6.00004ZM17 6C17 5.26362 17.597 4.66667 18.3334 4.66667H29C29.7364 4.66667 30.3334 5.26362 30.3334 6C30.3334 6.73638 29.7364 7.33333 29 7.33333H18.3334C17.597 7.33333 17 6.73638 17 6ZM18.3334 19.3333C17.597 19.3333 17 19.9303 17 20.6667C17 21.403 17.597 22 18.3334 22H29C29.7364 22 30.3334 21.403 30.3334 20.6667C30.3334 19.9303 29.7364 19.3333 29 19.3333H18.3334ZM17 11.3333C17 10.597 17.597 10 18.3334 10H29C29.7364 10 30.3334 10.597 30.3334 11.3333C30.3334 12.0697 29.7364 12.6667 29 12.6667H18.3334C17.597 12.6667 17 12.0697 17 11.3333ZM18.3334 24.6667C17.597 24.6667 17 25.2636 17 26C17 26.7364 17.597 27.3333 18.3334 27.3333H29C29.7364 27.3333 30.3334 26.7364 30.3334 26C30.3334 25.2636 29.7364 24.6667 29 24.6667H18.3334Z" fill="#C8CDD2"/>
          </svg>
        </div>
        <div class="no-games-text">暂无游戏</div>
      </div>
    </template>
    <template v-else-if="isLoading">
      <SkeletonGameList />
    </template>
    <template v-else>
      <div class="game-list-container" ref="gameListScroll">
        <GameItem
          v-for="game in gameList"
          :key="game.appid"
          :game="game"
          :isCurrentGame="currentGame === game.appid"
          @click="handleClickGame(game)"
        />
      </div>
    </template>

    <Popover
      :triggerRef="addGamePopoverTriggerRef"
      :triggerType="'click'"
      :showDelay="0"
      :hideDelay="0"
      :clickHidden="true"
      placement="right-start"
      :offset="{ x: 10, y: 10 }"
      :absolutePos="{
        top: 4,
      }"
    >
      <div
        class="add-game-popover"
        @click.stop
      >
        <div class="add-game-popover-content">
          <div
            class="add-game-popover-content-item"
            v-for="item in addPopoverConfigs"
            :key="item.key"
          >
            <div class="add-game-popover-content-item-label">
              {{ item.desc }}
            </div>
            <div class="add-game-popover-content-item-options">
              <div
                class="add-game-popover-content-item-option"
                v-for="option in item.options"
                :key="option.key"
              >
                <Selector
                  v-if="option.options"
                  class="option-selector"
                  v-model="option.value"
                  :options="transformOptions(option.options)"
                  :appendToBody="false"
                  @select="(subOption) => handleClickAddPopoverOption(item.key, option, subOption)"
                >
                  <div
                    class="add-game-popover-content-item-option-wrapper"
                    :class="{
                      'option-active': option.value
                    }"
                  >
                    <div
                      class="option-icon"
                      v-if="option.icon"
                    >
                      <img
                        :src="option.icon"
                        alt=""
                      />
                    </div>
                    <div
                      class="option-label"
                      v-if="option.desc"
                    >
                      {{ option.value ? option.options.find(op => op.key === option.value).desc : option.desc }}
                      <i class="iconfont icon-common-arrow-down-filled" v-if="option.options"></i>
                    </div>
                  </div>
                </Selector>
                <div v-else
                  class="add-game-popover-content-item-option-wrapper"
                  :class="{
                    'option-active': isOptionActice(option, item.key)
                  }"
                  @click.stop="handleClickAddPopoverOption(item.key, option)"
                >
                  <div
                    class="option-icon"
                    v-if="option.icon"
                  >
                    <img
                      :src="option.icon"
                      alt=""
                    />
                  </div>
                  <div
                    class="option-label"
                    v-if="option.desc"
                  >
                    {{ option.desc }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Popover>
  </div>
</template>

<script setup name="GameSideBar">
import { ref, onMounted, computed, onUnmounted, watch, nextTick } from 'vue';
import GameItem from './components/GameItem.vue';
import { getListFilterOptions } from '_api/steam'
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { throttle, debounce } from '@heybox-app-web-shared/utils';
import SkeletonSidebar from '@heybox-app-web-shared/components/library-skeleton/components/SkeletonSidebar.vue';
import SkeletonGameList from '@heybox-app-web-shared/components/library-skeleton/components/SkeletonGameList.vue';

const props = defineProps({
  isSteamInstalled: {
    type: Boolean,
    default: true
  }
});

const router = useRouter();
const store = useStore();
const gameList = computed(() => {
  const games = store.getters.currentSidebarGames;
  return games.filter(game => game.name && game.name.trim() !== '');
});
const gameListScroll = ref(null);

const isLoading = ref(true);
const hasData = computed(() => gameList.value.length > 0);

watch(hasData, (newVal) => {
  if (newVal) {
    isLoading.value = false;
  }
}, { immediate: true });

const searchStr = ref('');
const currentOrder = ref('by_2week');
const orderPopoverConfigs = ref([
  {
    label: '按字母排序',
    value: 'by_abc',
  },
  {
    label: '两周时长',
    value: 'by_2week',
  },
]);
const currentOrderLabel = computed(() => {
  return orderPopoverConfigs.value.find(item => item.value === currentOrder.value)?.label;
});
const currentGame = computed(() => {
  return store.state.current_game
});
const orderPopoverTriggerRef = ref(null);
const addGamePopoverTriggerRef = ref(null);

const addPopoverConfigs = ref([]);

const addPopoverForm = ref({});

const showAddGamePopover = ref(false);
const filterKeys = ref([]);

let popoverTimer = null;

// const handleClickFilterList = () => {};

const handleSearchAppList = () => {
  if(!searchStr.value && !filterKeys.value.length) {
    store.commit('setFilters', null)
    store.commit('resetFilterGameList')
    return
  }
  store.commit('setFilters', {
    q: searchStr.value,
    filter_options: addPopoverForm.value
  })
  store.commit('resetFilterGameList')
  store.dispatch('searchGames')
}

watch(searchStr, debounce(handleSearchAppList, 200))

const handleClickGame = ({ appid }) => {
  store.commit('setCurrentGame', appid)
  router.push(`/app/detail/${appid}`);
};

const isOptionActice = (option, field) => {
  if(option.options) {
    return false
  } else if(option.key === 'all' && !addPopoverForm.value[field]) {
    return true
  } else if(addPopoverForm.value[field] && addPopoverForm.value[field].keys.includes(option.key)) {
    return true
  } else {
    return false
  }
}

const handleBackHome = () => {
  store.commit('setCurrentGame', '')
  router.push('/app/home');
};

const clearPopoverTimer = () => {
  clearTimeout(popoverTimer);
  popoverTimer = null;
};

const handleChangeOrder = (option) => {
  currentOrder.value = option.value;
  store.commit('setGameOrder', option.value)
  store.commit('resetGameList')
  store.dispatch('getSteamGames')
  if(store.state.filters) {
    store.commit('resetFilterGameList')
    store.dispatch('searchGames')
  }
  // handleChangeShowOrderPopover(null, false);
  // orderPopoverTriggerRef.value.click()
  gameListScroll.value.scrollTop = 0;
};

// 初始化表单
const handleResetForm = () => {
  filterKeys.value.forEach(item => {
    handleRemoveFilter({
      field: item.field,
      key: item.key,
    })
  })
  handleSearchAppList()
};

const handleClickAddPopoverOption = (field, option, subOption = null) => {
  if (option.options) {
    // 如果为下拉选项，则需要先删除下拉框的其他选项
    let keys = addPopoverForm.value[field] && addPopoverForm.value[field].keys
    if(keys) {
      if(keys.includes(subOption.value)) return
      option.options.forEach(item => {
        handleRemoveFilter({
          field,
          key: item.key,
        }, false, false)
      })
      // 清理完下拉框选项可能表单项会被删除
      if(!addPopoverForm.value[field]) {
        addPopoverForm.value[field] = {
          keys: [subOption.value],
        }
      } else {
        addPopoverForm.value[field].keys.push(subOption.value)
      }

    } else {
      addPopoverForm.value[field] = {
        keys: [subOption.value],
      }
    }
    filterKeys.value.push({
      field,
      key: subOption.value,
      desc: subOption.label,
    })
  } else if(option.key === 'all') {
    let keys = addPopoverForm.value[field] && addPopoverForm.value[field].keys
    if(keys) {
      keys.forEach(key => {
        handleRemoveFilter({
          field,
          key,
        }, true, false)
      })
    }
  } else {
    let keys = addPopoverForm.value[field] && addPopoverForm.value[field].keys
    if(keys) {
      if(keys.includes(option.key)) {
        handleRemoveFilter({
          field,
          key: option.key,
          desc: option.desc,
        }, true, false)
      } else {
        keys.push(option.key)
        filterKeys.value.push({
          field,
          key: option.key,
          desc: option.desc,
        })
      }
    } else {
      addPopoverForm.value[field] = {
        keys: [option.key],
      }
      filterKeys.value.push({
        field,
        key: option.key,
        desc: option.desc,
      })
    }
  }
  handleSearchAppList()
};

const handleRemoveFilter = (option, isClearSubOption = true, needSearch = true) => {
  filterKeys.value = filterKeys.value.filter(item => item.key !== option.key)
  if(addPopoverForm.value[option.field]) {
    addPopoverForm.value[option.field].keys = addPopoverForm.value[option.field].keys.filter(key => key !== option.key)
    if(addPopoverForm.value[option.field].keys.length === 0) {
      delete addPopoverForm.value[option.field]
    }
  }
  // 清除选择框的选项
  if(isClearSubOption) {
    let options = addPopoverConfigs.value.find(item => item.key === option.field)?.options
    if(options) {
      options.forEach(item => {
        if(item.options) {
          let keys = item.options.map(op => op.key)
          if(keys.includes(option.key)) {
            item.value = null
          }
        }
      })
    }
  }
  if(needSearch) {
    handleSearchAppList()
  }
}

const transformOptions = (options) => {
  return options.map(item => ({
    label: item.desc,
    value: item.key,
  }))
}

onMounted(async () => {
  let res = await getListFilterOptions()
  console.log('[getListFilterOptions] res', res)
  if(res.data.status === 'ok') {
    console.log('options', res.data.result)
    addPopoverConfigs.value = res.data.result.options
  }
});

onUnmounted(() => {
  gameListScroll.value.removeEventListener('scroll', handleScroll);
});

const handleScroll = throttle(() => {
  if(gameListScroll.value.scrollTop + gameListScroll.value.clientHeight >= gameListScroll.value.scrollHeight - 200) {
    store.dispatch('getSteamGames')
  }
}, 100);

watch(isLoading, async (newVal) => {
  if(!newVal) {
    await nextTick();
    if (gameListScroll.value) {
      gameListScroll.value.addEventListener('scroll', handleScroll);
    }
  }
})
</script>

<style lang="scss">
.cpt-game-sidebar {
  width: 100%;
  height: 100%;
  .header-row {
    width: 100%;
    height: 52px;
    padding: 0px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(---general-color-stroke-1, #f1f2f3);
    .header-name {
      font-size: 16px;
      font-weight: 600;
      line-height: 22px;
      cursor: pointer;
    }
    .header-add-btn {
      display: flex;
      padding: 7px;
      align-items: center;
      gap: 10px;
    }
  }
  .search-wrapper {
    width: 100%;
    display: flex;
    padding: 8px 8px 0px 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;
    .search-input-wrapper {
      padding: 0 8px;
      width: 100%;
    }
    .search-input {
      width: 100%;
      display: flex;
      height: 32px;
      padding: 7px 12px;
      align-items: center;
      gap: 3px;
      flex: 1 0 0;
      border-radius: var(---32, 5px);
      background: $general-color-bg-3;
      border: 1px solid $general-color-stroke-2;
      input {
        display: flex;
        flex: 1 0 0;
        outline: none;
        border: none;
        background: transparent;
        font-size: 14px;
        line-height: 20px;
        color: var(---general-color-text-1, #111111);
        &::placeholder {
          color: var(---general-color-text-2, #8c9196);
        }
      }
    }
    .filter-container {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 8px;
      & > div {
        cursor: pointer;
      }
      .filter-option {
        flex: 1;
        height: 24px;
        .filter-option-label {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 3px;
          .iconfont {
            font-size: 12px;
            color: $general-color-text-3;
          }
          &.disabled {
            cursor: not-allowed;
            opacity: 0.5;
          }
        }
        span {
          color: $general-color-text-3;
          text-align: center;
          font-size: 12px;
          line-height: 16px;
        }
      }
      .vertical-split-line {
        width: 1px;
        height: 16px;
        background-color: $general-color-stroke-1;
      }
      .filter-list-btn {
        flex: 1;
        height: 24px;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 3px;
        span {
          color: $general-color-text-3;
          text-align: center;
          font-size: 12px;
          line-height: 16px;
        }
        .iconfont {
          color: $general-color-text-3;
          font-size: 12px;
        }
      }
    }
  }
  .divider {
    margin: 6px 16px;
    height: 1px;
    align-self: stretch;
    background-color: $general-color-stroke-2;
  }
  .game-list-container {
    width: 100%;
    height: calc(100% - 135px);
    overflow-y: scroll;
    padding: 0 0 8px 8px;
  }
  .filter-list-container {
    width: 100%;
    display: flex;
    gap: 4px;
    padding: 4px 8px;
    justify-content: space-between;
    .filter-list {
      flex: 1;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 6px;
      .filter-list-item {
        padding: 3px 4px 3px 6px;
        cursor: pointer;
        border-radius: $general-size-radius-3;
        text-align: center;
        background-color: $general-color-bg-0;
        border: 1px solid $general-color-text-3;
        color: $general-color-text-1;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        display: flex;
        align-items: center;
        gap: 4px;
        .iconfont {
          width: 14px;
          height: 14px;
          line-height: 14px;
          font-size: 8px;
          color: $general-color-text-3;
          font-weight: 500;
        }
      }
    }
    .reset-btn {
      cursor: pointer;
      text-align: center;
      color: $general-color-text-2;
      font-family: "PingFang SC";
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}

.order-popover {
  width: 120px;
  border-radius: var(---general-size-radius-8, 8px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  background-color: var(---general-color-bg-4, #fff);
  .order-popover-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 4px;
    .order-popover-content-item {
      padding: 4px 12px;
      cursor: pointer;
      border-radius: 5px;
      text-align: center;
      &.order-popover-content-item-active {
        background-color: var(---general-color-bg-2, #f7f8f9);
        font-weight: 700;
      }
      &:hover {
        background-color: var(---general-color-bg-2, #f7f8f9);
      }
    }
  }
}

.add-game-popover {
  width: 383px;
  display: flex;
  padding: 16px 4px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  border-radius: var(---general-size-radius-8, 8px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
  background-color: var(---general-color-bg-4, #fff);
  .add-game-popover-content {
    display: flex;
    width: 100%;
    padding: 0px 12px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 20px;
    .add-game-popover-content-item {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
      width: 100%;
      .add-game-popover-content-item-label {
        color: var(---general-color-text-3, #8c9196);
        font-size: 13px;
        line-height: 18px;
      }
      .add-game-popover-content-item-options {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
        width: 100%;
        .add-game-popover-content-item-option-wrapper {
          display: flex;
          padding: 7px 12px;
          align-items: center;
          gap: 4px;
          border-radius: 3px;
          background: var(---general-color-bg-2, #f7f8f9);
          cursor: pointer;
          &.option-active {
            background: var(
              ---,
              linear-gradient(
                46deg,
                var(---greadient-color-primary-left, #464b50) -0.9%,
                var(---greadient-color-primary-right, #14191e) 100.9%
              )
            );
            .option-label {
              color: var(---general-color-text-6, #fff);
            }
          }
          .option-icon {
            width: 16px;
            height: 16px;
            img {
              width: 100%;
              height: 100%;
            }
          }
          .option-label {
            display: flex;
            align-items: center;
            gap: 3px;
            color: var(---general-color-text-2, #64696e);
            font-size: 12px;
            line-height: 16px;
            white-space: nowrap;
            .iconfont {
              font-size: 8px;
            }
          }
          .option-more {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
    }
  }
  .add-game-popover-footer {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 10px;
    .add-game-popover-footer-btn {
      flex: 1;
      height: 40px;
      border-radius: 5px;
      text-align: center;
      font-size: 14px;
      font-weight: 500;
      line-height: 40px;

      cursor: pointer;
      &.cancel {
        background: var(---general-color-bg-2, #f7f8f9);
        color: var(---general-color-primary-100, #14191e);
      }
      &.confirm {
        background: var(---general-color-primary-100, #14191e);
        background: var(
          ---,
          linear-gradient(46deg, #464b50 -0.9%, #14191e 100.9%)
        );
        color: var(---general-color-text-6, #fff);
      }
    }
  }
}

.no-games-placeholder {
  display: flex;
  padding: 50px 36px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  flex: 1 0 0;
  align-self: stretch;
  color: #8C9196;

  .no-games-icon {
    opacity: 0.6;
  }

  .no-games-text {
    color: var(---general-color-text-3, #8C9196);
    text-align: center;
    
    font-family: "PingFang SC";
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 17px;
  }
}
</style>
