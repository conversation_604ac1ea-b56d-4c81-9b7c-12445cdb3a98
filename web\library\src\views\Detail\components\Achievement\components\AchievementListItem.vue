<template>
  <div
    class="sub-comp-block"
    :class="type"
    @click="handleClickAchievementGroup"
  >
    <div class="sub-comp-block-title">
      <span class="sub-title" style="margin-right: 4px">
        {{ title }} {{ achievedCount }}/{{ totalCount }} {{ subtitle }}
      </span>
    </div>
    <div class="sub-comp-list" :class="{ expanded: isExpanded }">
      <AchievementItem
        v-for="(item, index) in displayedList"
        :key="index"
        :item="item"
        @click.stop="handleClickAchievementItem(item)"
      />
      <!-- 展开时显示收起按钮 -->
      <div
        v-if="isExpanded"
        class="collapse-btn"
        @click.stop="toggleExpanded"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M8.46967 6.21967C8.76256 5.92678 9.23744 5.92678 9.53033 6.21967L14.0303 10.7197C14.3232 11.0126 14.3232 11.4874 14.0303 11.7803C13.7374 12.0732 13.2626 12.0732 12.9697 11.7803L9 7.81066L5.03033 11.7803C4.73744 12.0732 4.26256 12.0732 3.96967 11.7803C3.67678 11.4874 3.67678 11.0126 3.96967 10.7197L8.46967 6.21967Z" fill="white"/>
        </svg>
      </div>
      <!-- 默认状态显示+数字按钮 -->
      <div
        v-else-if="remainingCount > 0"
        class="more-count"
        @click.stop="toggleExpanded"
      >
        +{{ remainingCount }}
      </div>
    </div>
    <div class="right-bg-img" v-if="showBackgroundImage">
      <img
        src="../../../assets/imgs/cup.png"
        alt=""
      />
    </div>
  </div>
</template>

<script setup name="AchievementListItem">
import { ref, computed } from 'vue';
import AchievementItem from './AchievementItem.vue';

const props = defineProps({
  type: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  subtitle: {
    type: String,
    required: true,
  },
  list: {
    type: Array,
    default: () => [],
  },
  showBackgroundImage: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['click-group', 'click-item']);

const isExpanded = ref(false);

// 每行显示的成就数量
const ITEMS_PER_ROW = 7;

const achievedCount = computed(() => {
  return props.list.filter(item => item.achieved === true).length;
});

const totalCount = computed(() => {
  return props.list.length;
});

const displayedList = computed(() => {
  if (isExpanded.value) return props.list;
  return props.list.slice(0, ITEMS_PER_ROW - 1);
});

const remainingCount = computed(() => {
  const remaining = props.list.length - (ITEMS_PER_ROW - 1);
  return remaining > 0 ? remaining : 0;
});

const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
};

const handleClickAchievementGroup = () => {
  emit('click-group', props.type);
};

const handleClickAchievementItem = (item) => {
  emit('click-item', item);
};
</script>

<style lang="scss" scoped>
.sub-comp-block {
  position: relative;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  cursor: pointer;
  overflow: hidden;

  &.gold {
    background: linear-gradient(86deg, #FBB63F 8.29%, #FFDE6A 98.24%);
  }

  &.silver {
    background: linear-gradient(86deg, #AECFFF 8.29%, #C4DAED 98.24%);
  }

  .sub-comp-block-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .sub-title {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .sub-comp-list {
    position: relative;
    z-index: 2;
    display: flex;
    gap: 4px;
    overflow: hidden;

    &.expanded {
      flex-wrap: wrap;
    }

    .more-count {
      display: flex;
      padding: 8px 5px;
      justify-content: center;
      align-items: center;
      width: 34px;
      height: 34px;
      border-radius: 6px;
      background: rgba(0, 0, 0, 0.20);
      cursor: pointer;
      color: #FFF;
      text-align: center;
      font-family: "Helvetica Neue";
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 18px;
    }

    .collapse-btn {
      display: flex;
      width: 34px;
      height: 34px;
      padding: 8px;
      justify-content: center;
      align-items: center;
      border-radius: $general-size-radius-3;
      background: rgba(0, 0, 0, 0.20);
      flex-shrink: 0;
    }
  }

  .right-bg-img {
    display: flex;
    width: 44px;
    height: 72px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;

    img {
      width: 48px;
      height: 48px;
    }
  }
}
</style>
