<template>
  <div class="view-home-page">
    <template v-if="isLoading">
      <SkeletonAccounts :isLoggedIn="isSteamInstalled" />
      <SkeletonGames v-if="isSteamInstalled" />
      <SkeletonGamesUnlogged v-else />
    </template>

    <template v-else>
      <VirtualList
        ref="virtualListRef"
        :items="games"
        :items-per-row="cardConfig.cardsPerRow"
        :item-height="cardHeight"
        :row-gap="CARD_GAP"
        :buffer-rows="2"
        :get-item-key="(game) => game.appid"
        :on-reach-bottom="handleReachBottom"
        class="games-virtual-list"
        @scroll="handleVirtualListScroll"
      >
        <template #top>
          <div
            class="accounts-header-wrapper"
            :class="{'accounts-header-wrapper-opacity': accountsHeaderOpacity < 0.5}"
          >
            <div class="accounts-header-bg">
              <div class="accounts-header-bg-inner" :style="{'opacity': accountsHeaderOpacity}"></div>
              <div class="accounts-header-bg-white" :style="{'opacity': 1 - accountsHeaderOpacity}"></div>
            </div>
            <div class="accounts-header">
              <div class="accounts-header-title">账号管理</div>
              <div @click="handleAddAccount" class="accounts-add-btn">添加账号</div>
            </div>
          </div>
          <div
            class="accounts-wrapper"
          >
            <div class="accounts-container">
              <div class="accounts-container-scroll" ref="accountsContainerScroll">
                <AccountCard
                  v-for="(account, index) in showAccounts"
                  :key="account.id"
                  :account="account"
                  :showAll="index === 0"
                  :cardConfig="cardConfig"
                  @switchAccount="handleSwitchAccount"
                  @offlineAccount="handleOfflineAccount"
                  @remarkAccount="handleRemarkAccount"
                  @deleteAccount="handleDeleteAccount"
                />
              </div>
              <div
                class="left-arrow"
                v-show="accountPagination > 0"
                @click="handlePagination('prev')"
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M10.1257 1.21956C10.4184 1.49607 10.4316 1.95757 10.1551 2.25034L5.66963 6.99968L10.1551 11.749C10.4316 12.0418 10.4184 12.5033 10.1257 12.7798C9.83289 13.0563 9.37139 13.0431 9.09489 12.7503L4.13655 7.50034C3.8711 7.21932 3.8711 6.78003 4.13655 6.49901L9.09489 1.24901C9.37139 0.95624 9.83289 0.943054 10.1257 1.21956Z"
                    fill="white"
                    fill-opacity="0.5"
                  />
                </svg>
              </div>
              <div
                class="right-arrow"
                v-show="lastPage"
                @click="handlePagination('next')"
              >
                <svg
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M3.87434 1.21956C3.58156 1.49607 3.56838 1.95757 3.84489 2.25034L8.33037 6.99968L3.84489 11.749C3.56838 12.0418 3.58156 12.5033 3.87434 12.7798C4.16711 13.0563 4.62861 13.0431 4.90511 12.7503L9.86345 7.50034C10.1289 7.21932 10.1289 6.78003 9.86345 6.49901L4.90511 1.24901C4.62861 0.95624 4.16711 0.943054 3.87434 1.21956Z"
                    fill="white"
                    fill-opacity="0.5"
                  />
                </svg>
              </div>
            </div>
          </div>
        </template>
        <template #default="{ item: game }">
          <GameCard
            :game="game"
            :style="{ width: `${cardConfig.width}px` }"
            @clickCard="handleClickGameCard"
          />
        </template>
      </VirtualList>
      <div v-if="accountsHeaderOpacity === 0" class="scroll-top-btn" @click="handleScrollTop">
        <div class="scroll-top-btn-icon">
          <svg width="24" height="24" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.0005 5.625C17.1721 5.625 16.5005 6.29253 16.5005 7.11596L16.5005 28.884C16.5005 29.7075 17.1721 30.375 18.0005 30.375C18.8289 30.375 19.5005 29.7075 19.5005 28.884L19.5005 7.11596C19.5005 6.29253 18.8289 5.625 18.0005 5.625Z" fill="#C8CDD2"/>
            <path fill-rule="evenodd" clip-rule="evenodd" d="M19.0612 6.06169C18.4754 5.47944 17.5256 5.47944 16.9398 6.06169L7.93983 15.0075C7.35404 15.5897 7.35404 16.5338 7.93983 17.116C8.52561 17.6983 9.47536 17.6983 10.0611 17.116L18.0005 9.2245L25.9398 17.116C26.5256 17.6983 27.4754 17.6983 28.0612 17.116C28.6469 16.5338 28.6469 15.5897 28.0612 15.0075L19.0612 6.06169Z" fill="#C8CDD2"/>
          </svg>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup name="ViewHome">
import AccountCard from './components/AccountCard/index.vue';
import GameCard from './components/GameCard/index.vue';
import VirtualList from '_com/func/VirtualList.vue';
import SteamManager from '_js/steam-manager';
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { throttle } from '@heybox-app-web-shared/utils';
import { MAX_CARD_WIDTH, MIN_CARD_WIDTH, CARD_GAP } from '_constant/index';
import SkeletonAccounts from '@heybox-app-web-shared/components/library-skeleton/components/SkeletonAccounts.vue';
import SkeletonGames from '@heybox-app-web-shared/components/library-skeleton/components/SkeletonGames.vue';
import SkeletonGamesUnlogged from '@heybox-app-web-shared/components/library-skeleton/components/SkeletonGamesUnlogged.vue';

const props = defineProps({
  isSteamInstalled: {
    type: Boolean,
    default: true
  }
});

const router = useRouter();
const store = useStore();

const accounts = computed(() => store.state.steam_accounts);
const games = computed(() => store.state.steam_games.filter(game => game.name !== ''));
const accountPagination = ref(0);
const accountsContainerScroll = ref(null);
const virtualListRef = ref(null);
let scrollTopTimer = null;
let containerResizeObserver = null;

const isLoading = computed(() => store.state.is_library_loading);

// 虚拟列表滚动位置
const virtualListScrollTop = ref(0);

// 计算卡片大小和每行显示数量
const cardConfig = ref({
  width: MAX_CARD_WIDTH,
  cardsPerRow: 1
});

// 根据卡片宽度计算卡片高度（宽高比 15:22）
const cardHeight = computed(() => {
  return (cardConfig.value.width * 22) / 15;
});

// 计算行高（卡片高度 + gap）
const rowHeight = computed(() => {
  return cardHeight.value + CARD_GAP;
});

const accountsHeaderOpacity = computed(() => {
  const ACCOUNT_HEIGHT = 270
  const ACCOUNT_HEADER_HEIGHT = 52
  const ACCOUNT_CONTAIN_HEIGHT = 218
  let height = ACCOUNT_HEIGHT - virtualListScrollTop.value
  height = height > ACCOUNT_HEADER_HEIGHT ? height : ACCOUNT_HEADER_HEIGHT
  let opacity = (height - ACCOUNT_HEADER_HEIGHT) / (ACCOUNT_CONTAIN_HEIGHT)
  return opacity
})

// 计算卡片配置
const calculateCardConfig = () => {
  const containerWidth = virtualListRef.value.$el.clientWidth - 48; // 减去左右padding
  const maxCardsPerRow = Math.floor((containerWidth + CARD_GAP) / (MIN_CARD_WIDTH + CARD_GAP));
  const minCardsPerRow = Math.floor((containerWidth + CARD_GAP) / (MAX_CARD_WIDTH + CARD_GAP));

  // 计算每行卡片数量
  const cardsPerRow = Math.max(1, Math.max(maxCardsPerRow, minCardsPerRow));

  // 计算卡片宽度
  const cardWidth = (containerWidth - (cardsPerRow - 1) * CARD_GAP) / cardsPerRow;

  cardConfig.value = {
    width: cardWidth,
    cardsPerRow
  };
};

const currentAccountCardLength = computed(() => {
  let cardsPerRow = cardConfig.value.cardsPerRow
  return cardsPerRow <= 4 ? 3 : cardsPerRow >= 8 ? 5 : cardsPerRow - 2
})

const lastPage = computed(() => {
  return showAccounts.value.length + currentAccountCardLength.value - 1 - (accountPagination.value + 1) * cardConfig.value.cardsPerRow > 0
})

const showAccounts = computed(() => {
  let list = []
  let currentAccount = accounts.value.find(account => account.steam_id === store.state.current_account)
  if(currentAccount) {
    list.push(currentAccount)
  }
  let otherAccounts = accounts.value.filter(account => account.steam_id !== store.state.current_account)
  otherAccounts.sort((a, b) => Number(b.timestamp) - Number(a.timestamp))
  list.push(...otherAccounts)
  return list
});

const handlePagination = (type) => {
  let el = accountsContainerScroll.value
  if (type === 'prev') {
    accountPagination.value--;
    el.scrollTo({
      left: el.clientWidth * accountPagination.value + CARD_GAP * accountPagination.value,
      behavior: 'smooth'
    })
  } else {
    accountPagination.value++;
    el.scrollTo({
      left: el.clientWidth * accountPagination.value + CARD_GAP * accountPagination.value,
      behavior: 'smooth'
    })
  }
};

const handleAddAccount = () => {
  window.popupManagerAPI.show({
    type: 'Dialog',
    cptName: 'SteamLoginDialog',
  });
}

const handleClickGameCard = (game) => {
  const { appid } = game;
  store.commit('setCurrentGame', appid)
  router.push({
    path: `/app/detail/${appid}`,
  });
};

const handleSwitchAccount = (account) => {
  SteamManager.setCurrentAccount(account)
  resetAccountScroll()
}

const handleOfflineAccount = (account) => {
  SteamManager.setOfflineMode(account)
  resetAccountScroll()
}
const handleRemarkAccount = (account) => {
  console.log(account)
}
const handleDeleteAccount = (account) => {
  SteamManager.deleteAccount(account)
  resetAccountScroll()
}

const handleVirtualListScroll = (scrollInfo) => {
  virtualListScrollTop.value = virtualListRef.value.$el.scrollTop;
  if(scrollTopTimer) {
    clearTimeout(scrollTopTimer)
  }

  scrollTopTimer = setTimeout(() => {
    virtualListScrollTop.value = virtualListRef.value.$el.scrollTop;
  }, 100)
}

const handleScrollTop = () => {
  virtualListRef.value.$el.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// 滚动到底部时的处理函数
const handleReachBottom = () => {
  store.dispatch('getSteamGames');
};

const handleResize = throttle(() => {
  calculateCardConfig();
  if(accountPagination.value) {
    resetAccountScroll()
  }
}, 10);

const resetAccountScroll = () => {
  accountPagination.value = 0
  accountsContainerScroll.value.scrollTo({
    left: 0,
    behavior: 'smooth'
  })
}

const initCardConfig = async () => {
  await nextTick()
  // cardConfig 宽度计算依赖 virtualListRef.value.$el
  if (virtualListRef.value && virtualListRef.value.$el) {
    calculateCardConfig();

    if (!containerResizeObserver) {
      containerResizeObserver = new ResizeObserver(() => {
        calculateCardConfig();
      });
      containerResizeObserver.observe(virtualListRef.value.$el);
    }
  }
}

onMounted(async () => {
  // await store.dispatch('getSteamGames')

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);

  if (!isLoading.value) {
    await initCardConfig();
  }

  watch(isLoading, async (newVal) => {
    if (!newVal) {
      await initCardConfig();
    }
  })
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  clearTimeout(scrollTopTimer)

  if (containerResizeObserver) {
    containerResizeObserver.disconnect();
    containerResizeObserver = null;
  }
});

</script>

<style lang="scss">
.view-home-page {
  width: 100%;
  height: 100vh;
  overflow-y: scroll;
  overflow-x: hidden;
  position: relative;
  .accounts-header-wrapper {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    &.accounts-header-wrapper-opacity {
      .accounts-header-title {
        color: $general-color-text-1;
      }
      .accounts-add-btn {
        color: $general-color-text-1;
        background: $general-color-bg-1;
      }
    }

    .accounts-header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      .accounts-header-bg-inner {
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, #253f78 0%, #1b73a5 100%);
      }
      .accounts-header-bg-white {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        background: $general-color-bg-4;
      }
    }
    .accounts-header {
      display: flex;
      position: relative;
      z-index: 1;
      width: 100%;
      height: 52px;
      padding: 16px 24px 14px 24px;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      z-index: 1;
    }
    .accounts-header-title {
      color: var(--white-gamerecord-color-white-100a, #fff);
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      letter-spacing: 0.16px;
    }
    .accounts-add-btn {
      cursor: pointer;
      display: inline-flex;
      height: 30px;
      padding: 6px 10px;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      border-radius: 4px;
      background-color: var(
        --white-gamerecord-color-white-20a,
        rgba(255, 255, 255, 0.2)
      );

      color: var(--white-gamerecord-color-white-100a, #fff);
      text-align: center;
      font-size: 12px;
      line-height: 16px;
      letter-spacing: 0.12px;
    }
  }

  .accounts-wrapper {
    height: 218px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    overflow: hidden;
    transition: height 0.3s ease-out;
    background: linear-gradient(90deg, #253f78 0%, #1b73a5 100%);
    z-index: 0;

    .accounts-container {
      width: 100%;
      flex: 1;
      padding: 24px;
      position: relative;

      .accounts-container-scroll {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 16px;
        overflow: hidden;
      }
      .left-arrow,
      .right-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }

      .left-arrow {
        left: 5px;
      }
      .right-arrow {
        right: 6px;
      }
    }
  }
  .games-virtual-list {
    width: calc(100% + 8px);
    overflow-x: hidden;
    .virtual-list-container {
      padding: 24px;
      width: calc(100% + 58px); // 留白防止页面缩小时出现闪烁问题
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
      align-content: flex-start;
    }
  }
  .scroll-top-btn {
    position: fixed;
    bottom: 16px;
    right: 16px;
    width: 36px;
    height: 36px;
    padding: 6px;
    background: var(--general-color-primary-0, #fff);
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.15);
  }
}
</style>
